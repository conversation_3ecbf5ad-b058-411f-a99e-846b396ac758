plugins {
    id 'com.android.application'
    id 'org.jetbrains.kotlin.android'
}

publishing {
    publications {
        // 这个mavenJava可以随便填，只是一个任务名字而已
        // MavenPublication必须有，这个是调用的任务类
        mavenJava(MavenPublication) {
            // 这里头是artifacts的配置信息，不填会采用默认的
            groupId = 'org.gradle.sample'
            artifactId = 'library'
            version = '1.1'
        }
    }
}

android {
    namespace 'com.zlw.audio_recorder'
    compileSdk 33

    defaultConfig {
        applicationId "com.zlw.audio_recorder"
        minSdk 24
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "androidx.test.runner.AndroidJUnitRunner"
        vectorDrawables {
            useSupportLibrary true
        }
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android-optimize.txt'), 'proguard-rules.pro'
        }
    }
    compileOptions {
        sourceCompatibility JavaVersion.VERSION_1_8
        targetCompatibility JavaVersion.VERSION_1_8
    }
    kotlinOptions {
        jvmTarget = '1.8'
    }
    buildFeatures {
        compose true
    }
    composeOptions {
        kotlinCompilerExtensionVersion '1.4.3'
    }
    packaging {
        resources {
            excludes += '/META-INF/{AL2.0,LGPL2.1}'
        }
    }
}

dependencies {
    implementation 'androidx.core:core-ktx:1.9.0'
    implementation 'androidx.lifecycle:lifecycle-runtime-ktx:2.6.2'
    implementation 'androidx.activity:activity-compose:1.7.2'
    implementation platform('androidx.compose:compose-bom:2023.03.00')
    implementation 'androidx.compose.ui:ui'
    implementation 'androidx.compose.ui:ui-graphics'
    implementation 'androidx.compose.ui:ui-tooling-preview'
    implementation 'androidx.compose.material3:material3'

    // Material Design Components for traditional Views
    implementation 'com.google.android.material:material:1.9.0'
    implementation 'androidx.viewpager2:viewpager2:1.0.0'
    implementation 'androidx.fragment:fragment:1.6.1'

    implementation 'com.blankj:utilcodex:1.31.1'
    implementation 'com.yanzhenjie:permission:2.0.3'
    implementation 'com.github.zhaolewei:Logger:1.0.2'
    implementation project(path: ':recorderlib')

    // WebSocket for real-time speech recognition
    implementation 'com.squareup.okhttp3:okhttp:4.12.0'
}
1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.zlw.audio_recorder"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="24" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:6:5-71
12-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:6:22-68
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
13-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:7:5-81
13-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:8:5-80
14-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:8:22-77
15
16    <queries>
16-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:9:5-16:15
17        <intent>
17-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:10:9-12:18
18            <action android:name="android.intent.action.MAIN" />
18-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:25:17-69
18-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:25:25-66
19        </intent>
20        <intent>
20-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:13:9-15:18
21            <action android:name="android.intent.action.VIEW" />
21-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:14:13-65
21-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:14:21-62
22        </intent>
23    </queries>
24
25    <permission
25-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:24:5-27:47
26        android:name="com.zlw.audio_recorder.andpermission.bridge"
26-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:25:9-61
27        android:permissionGroup="com.zlw.audio_recorder.andpermission"
27-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:26:9-65
28        android:protectionLevel="signature" />
28-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:27:9-44
29
30    <uses-permission android:name="com.zlw.audio_recorder.andpermission.bridge" />
30-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:29:5-77
30-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:29:22-74
31
32    <permission
32-->[androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
33        android:name="com.zlw.audio_recorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
33-->[androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
34        android:protectionLevel="signature" />
34-->[androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
35
36    <uses-permission android:name="com.zlw.audio_recorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
36-->[androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
36-->[androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
37
38    <application
38-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:11:5-39:19
39        android:name="com.zlw.audio_recorder.base.MyApp"
39-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:12:9-35
40        android:allowBackup="true"
40-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:13:9-35
41        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
41-->[androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
42        android:debuggable="true"
43        android:extractNativeLibs="false"
44        android:icon="@mipmap/ic_launcher"
44-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:14:9-43
45        android:label="@string/app_name"
45-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:15:9-41
46        android:roundIcon="@mipmap/ic_launcher_round"
46-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:16:9-54
47        android:supportsRtl="true"
47-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:17:9-35
48        android:testOnly="true"
49        android:theme="@style/Theme.ZlwAudioRecorder" >
49-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:18:9-54
50        <activity
50-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:20:9-29:20
51            android:name="com.zlw.audio_recorder.MainActivity"
51-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:21:13-41
52            android:exported="true"
52-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:22:13-36
53            android:theme="@style/Theme.ZlwAudioRecorder" >
53-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:23:13-58
54            <intent-filter>
54-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:24:13-28:29
55                <action android:name="android.intent.action.MAIN" />
55-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:25:17-69
55-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:25:25-66
56
57                <category android:name="android.intent.category.LAUNCHER" />
57-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:27:17-77
57-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:27:27-74
58            </intent-filter>
59        </activity>
60        <activity
60-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:30:9-33:53
61            android:name="com.zlw.audio_recorder.TestHzActivity"
61-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:31:13-43
62            android:label="test"
62-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:32:13-33
63            android:screenOrientation="landscape" />
63-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:33:13-50
64
65        <service
65-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:35:9-38:40
66            android:name="com.zlw.main.recorderlib.recorder.RecordService"
66-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:36:13-75
67            android:enabled="true"
67-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:37:13-35
68            android:exported="false" />
68-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:38:13-37
69
70        <activity
70-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:19:9-24:75
71            android:name="com.blankj.utilcode.util.UtilsTransActivity4MainProcess"
71-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:20:13-83
72            android:configChanges="orientation|keyboardHidden|screenSize"
72-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:21:13-74
73            android:exported="false"
73-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:22:13-37
74            android:theme="@style/ActivityTranslucent"
74-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:23:13-55
75            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
75-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:24:13-72
76        <activity
76-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:25:9-31:75
77            android:name="com.blankj.utilcode.util.UtilsTransActivity"
77-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:26:13-71
78            android:configChanges="orientation|keyboardHidden|screenSize"
78-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:27:13-74
79            android:exported="false"
79-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:28:13-37
80            android:multiprocess="true"
80-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:29:13-40
81            android:theme="@style/ActivityTranslucent"
81-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:30:13-55
82            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
82-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:31:13-72
83
84        <provider
84-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:33:9-41:20
85            android:name="com.blankj.utilcode.util.UtilsFileProvider"
85-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:34:13-70
86            android:authorities="com.zlw.audio_recorder.utilcode.fileprovider"
86-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:35:13-73
87            android:exported="false"
87-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:36:13-37
88            android:grantUriPermissions="true" >
88-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:37:13-47
89            <meta-data
89-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:38:13-40:68
90                android:name="android.support.FILE_PROVIDER_PATHS"
90-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:39:17-67
91                android:resource="@xml/util_code_provider_paths" />
91-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:40:17-65
92        </provider>
93
94        <service
94-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:43:9-49:19
95            android:name="com.blankj.utilcode.util.MessengerUtils$ServerService"
95-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:44:13-81
96            android:exported="false" >
96-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:45:13-37
97            <intent-filter>
97-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:46:13-48:29
98                <action android:name="com.zlw.audio_recorder.messenger" />
98-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:17-69
98-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:25-66
99            </intent-filter>
100        </service>
101        <service
101-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:32:9-40:19
102            android:name="com.yanzhenjie.permission.bridge.BridgeService"
102-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:33:13-74
103            android:exported="false"
103-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:34:13-37
104            android:permission="com.zlw.audio_recorder.andpermission.bridge"
104-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:35:13-71
105            android:process=":permission" >
105-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:36:13-42
106            <intent-filter>
106-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:37:13-39:29
107                <action android:name="com.zlw.audio_recorder.andpermission.bridge" />
107-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:38:17-80
107-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:38:25-77
108            </intent-filter>
109        </service>
110
111        <activity
111-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:42:9-48:76
112            android:name="com.yanzhenjie.permission.bridge.BridgeActivity"
112-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:43:13-75
113            android:configChanges="orientation"
113-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:44:13-48
114            android:exported="false"
114-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:45:13-37
115            android:permission="com.zlw.audio_recorder.andpermission.bridge"
115-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:46:13-71
116            android:process=":permission"
116-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:47:13-42
117            android:theme="@style/Permission.Theme.Activity.Transparent" />
117-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:48:13-73
118
119        <provider
119-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:50:9-58:20
120            android:name="com.yanzhenjie.permission.FileProvider"
120-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:51:13-66
121            android:authorities="com.zlw.audio_recorder.file.path.share"
121-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:52:13-67
122            android:exported="false"
122-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:53:13-37
123            android:grantUriPermissions="true" >
123-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:54:13-47
124            <meta-data
124-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:38:13-40:68
125                android:name="android.support.FILE_PROVIDER_PATHS"
125-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:39:17-67
126                android:resource="@xml/permission_file_paths" />
126-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:40:17-65
127        </provider>
128        <provider
128-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
129            android:name="androidx.startup.InitializationProvider"
129-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
130            android:authorities="com.zlw.audio_recorder.androidx-startup"
130-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
131            android:exported="false" >
131-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
132            <meta-data
132-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
133                android:name="androidx.emoji2.text.EmojiCompatInitializer"
133-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
134                android:value="androidx.startup" />
134-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
135            <meta-data
135-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\a8db42fd17dc95f308887add3fd2816d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
136                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
136-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\a8db42fd17dc95f308887add3fd2816d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
137                android:value="androidx.startup" />
137-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\a8db42fd17dc95f308887add3fd2816d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
138            <meta-data
138-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
139                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
139-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
140                android:value="androidx.startup" />
140-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
141        </provider>
142
143        <receiver
143-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
144            android:name="androidx.profileinstaller.ProfileInstallReceiver"
144-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
145            android:directBootAware="false"
145-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
146            android:enabled="true"
146-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
147            android:exported="true"
147-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
148            android:permission="android.permission.DUMP" >
148-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
149            <intent-filter>
149-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
150                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
150-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
150-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
151            </intent-filter>
152            <intent-filter>
152-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
153                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
153-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
153-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
154            </intent-filter>
155            <intent-filter>
155-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
156                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
156-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
156-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
157            </intent-filter>
158            <intent-filter>
158-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
159                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
159-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
159-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
160            </intent-filter>
161        </receiver>
162    </application>
163
164</manifest>

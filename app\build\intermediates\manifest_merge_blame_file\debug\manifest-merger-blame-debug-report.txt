1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.zlw.audio_recorder"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="24"
9        android:targetSdkVersion="24" />
10
11    <uses-permission android:name="android.permission.INTERNET" />
11-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:5:5-67
11-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:5:22-64
12    <uses-permission android:name="android.permission.RECORD_AUDIO" />
12-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:6:5-71
12-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:6:22-68
13    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
13-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:7:5-81
13-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:7:22-78
14    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
14-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:8:5-80
14-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:8:22-77
15
16    <queries>
16-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:9:5-16:15
17        <intent>
17-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:10:9-12:18
18            <action android:name="android.intent.action.MAIN" />
18-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:25:17-69
18-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:25:25-66
19        </intent>
20        <intent>
20-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:13:9-15:18
21            <action android:name="android.intent.action.VIEW" />
21-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:14:13-65
21-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:14:21-62
22        </intent>
23    </queries>
24
25    <permission
25-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:24:5-27:47
26        android:name="com.zlw.audio_recorder.andpermission.bridge"
26-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:25:9-61
27        android:permissionGroup="com.zlw.audio_recorder.andpermission"
27-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:26:9-65
28        android:protectionLevel="signature" />
28-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:27:9-44
29
30    <uses-permission android:name="com.zlw.audio_recorder.andpermission.bridge" />
30-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:29:5-77
30-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:29:22-74
31
32    <permission
32-->[androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
33        android:name="com.zlw.audio_recorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
33-->[androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
34        android:protectionLevel="signature" />
34-->[androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
35
36    <uses-permission android:name="com.zlw.audio_recorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />
36-->[androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
36-->[androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
37
38    <application
38-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:11:5-39:19
39        android:name="com.zlw.audio_recorder.base.MyApp"
39-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:12:9-35
40        android:allowBackup="true"
40-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:13:9-35
41        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
41-->[androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
42        android:debuggable="true"
43        android:extractNativeLibs="false"
44        android:icon="@mipmap/ic_launcher"
44-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:14:9-43
45        android:label="@string/app_name"
45-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:15:9-41
46        android:roundIcon="@mipmap/ic_launcher_round"
46-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:16:9-54
47        android:supportsRtl="true"
47-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:17:9-35
48        android:theme="@style/Theme.ZlwAudioRecorder" >
48-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:18:9-54
49        <activity
49-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:20:9-29:20
50            android:name="com.zlw.audio_recorder.MainActivity"
50-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:21:13-41
51            android:exported="true"
51-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:22:13-36
52            android:theme="@style/Theme.ZlwAudioRecorder" >
52-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:23:13-58
53            <intent-filter>
53-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:24:13-28:29
54                <action android:name="android.intent.action.MAIN" />
54-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:25:17-69
54-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:25:25-66
55
56                <category android:name="android.intent.category.LAUNCHER" />
56-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:27:17-77
56-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:27:27-74
57            </intent-filter>
58        </activity>
59        <activity
59-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:30:9-33:53
60            android:name="com.zlw.audio_recorder.TestHzActivity"
60-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:31:13-43
61            android:label="test"
61-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:32:13-33
62            android:screenOrientation="landscape" />
62-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:33:13-50
63
64        <service
64-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:35:9-38:40
65            android:name="com.zlw.main.recorderlib.recorder.RecordService"
65-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:36:13-75
66            android:enabled="true"
66-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:37:13-35
67            android:exported="false" />
67-->D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:38:13-37
68
69        <activity
69-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:19:9-24:75
70            android:name="com.blankj.utilcode.util.UtilsTransActivity4MainProcess"
70-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:20:13-83
71            android:configChanges="orientation|keyboardHidden|screenSize"
71-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:21:13-74
72            android:exported="false"
72-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:22:13-37
73            android:theme="@style/ActivityTranslucent"
73-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:23:13-55
74            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
74-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:24:13-72
75        <activity
75-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:25:9-31:75
76            android:name="com.blankj.utilcode.util.UtilsTransActivity"
76-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:26:13-71
77            android:configChanges="orientation|keyboardHidden|screenSize"
77-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:27:13-74
78            android:exported="false"
78-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:28:13-37
79            android:multiprocess="true"
79-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:29:13-40
80            android:theme="@style/ActivityTranslucent"
80-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:30:13-55
81            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
81-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:31:13-72
82
83        <provider
83-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:33:9-41:20
84            android:name="com.blankj.utilcode.util.UtilsFileProvider"
84-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:34:13-70
85            android:authorities="com.zlw.audio_recorder.utilcode.fileprovider"
85-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:35:13-73
86            android:exported="false"
86-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:36:13-37
87            android:grantUriPermissions="true" >
87-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:37:13-47
88            <meta-data
88-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:38:13-40:68
89                android:name="android.support.FILE_PROVIDER_PATHS"
89-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:39:17-67
90                android:resource="@xml/util_code_provider_paths" />
90-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:40:17-65
91        </provider>
92
93        <service
93-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:43:9-49:19
94            android:name="com.blankj.utilcode.util.MessengerUtils$ServerService"
94-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:44:13-81
95            android:exported="false" >
95-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:45:13-37
96            <intent-filter>
96-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:46:13-48:29
97                <action android:name="com.zlw.audio_recorder.messenger" />
97-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:17-69
97-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:25-66
98            </intent-filter>
99        </service>
100        <service
100-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:32:9-40:19
101            android:name="com.yanzhenjie.permission.bridge.BridgeService"
101-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:33:13-74
102            android:exported="false"
102-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:34:13-37
103            android:permission="com.zlw.audio_recorder.andpermission.bridge"
103-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:35:13-71
104            android:process=":permission" >
104-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:36:13-42
105            <intent-filter>
105-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:37:13-39:29
106                <action android:name="com.zlw.audio_recorder.andpermission.bridge" />
106-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:38:17-80
106-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:38:25-77
107            </intent-filter>
108        </service>
109
110        <activity
110-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:42:9-48:76
111            android:name="com.yanzhenjie.permission.bridge.BridgeActivity"
111-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:43:13-75
112            android:configChanges="orientation"
112-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:44:13-48
113            android:exported="false"
113-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:45:13-37
114            android:permission="com.zlw.audio_recorder.andpermission.bridge"
114-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:46:13-71
115            android:process=":permission"
115-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:47:13-42
116            android:theme="@style/Permission.Theme.Activity.Transparent" />
116-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:48:13-73
117
118        <provider
118-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:50:9-58:20
119            android:name="com.yanzhenjie.permission.FileProvider"
119-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:51:13-66
120            android:authorities="com.zlw.audio_recorder.file.path.share"
120-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:52:13-67
121            android:exported="false"
121-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:53:13-37
122            android:grantUriPermissions="true" >
122-->[com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:54:13-47
123            <meta-data
123-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:38:13-40:68
124                android:name="android.support.FILE_PROVIDER_PATHS"
124-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:39:17-67
125                android:resource="@xml/permission_file_paths" />
125-->[com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:40:17-65
126        </provider>
127        <provider
127-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
128            android:name="androidx.startup.InitializationProvider"
128-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
129            android:authorities="com.zlw.audio_recorder.androidx-startup"
129-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
130            android:exported="false" >
130-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
131            <meta-data
131-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
132                android:name="androidx.emoji2.text.EmojiCompatInitializer"
132-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
133                android:value="androidx.startup" />
133-->[androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
134            <meta-data
134-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\a8db42fd17dc95f308887add3fd2816d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
135                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
135-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\a8db42fd17dc95f308887add3fd2816d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
136                android:value="androidx.startup" />
136-->[androidx.lifecycle:lifecycle-process:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\a8db42fd17dc95f308887add3fd2816d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
137            <meta-data
137-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
138                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
138-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
139                android:value="androidx.startup" />
139-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
140        </provider>
141
142        <receiver
142-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
143            android:name="androidx.profileinstaller.ProfileInstallReceiver"
143-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
144            android:directBootAware="false"
144-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
145            android:enabled="true"
145-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
146            android:exported="true"
146-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
147            android:permission="android.permission.DUMP" >
147-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
148            <intent-filter>
148-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
149                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
149-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
149-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
150            </intent-filter>
151            <intent-filter>
151-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
152                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
152-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
152-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
153            </intent-filter>
154            <intent-filter>
154-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
155                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
155-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
155-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
156            </intent-filter>
157            <intent-filter>
157-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
158                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
158-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
158-->[androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92
159            </intent-filter>
160        </receiver>
161    </application>
162
163</manifest>

package com.zlw.audio_recorder;

import android.app.Activity;
import android.content.Intent;
import android.media.AudioFormat;
import android.media.projection.MediaProjection;
import android.media.projection.MediaProjectionManager;
import android.os.Bundle;
import android.os.Environment;
import android.view.KeyEvent;
import android.view.View;
import android.widget.Button;
import android.widget.ImageButton;
import android.widget.TextView;
import android.widget.Toast;

import androidx.activity.ComponentActivity;
import androidx.activity.result.contract.ActivityResultContract;
import androidx.annotation.Nullable;
import androidx.appcompat.app.AppCompatActivity;
import androidx.fragment.app.Fragment;
import androidx.viewpager2.widget.ViewPager2;

import com.google.android.material.tabs.TabLayout;
import com.google.android.material.tabs.TabLayoutMediator;

import com.yanzhenjie.permission.AndPermission;
import com.yanzhenjie.permission.runtime.Permission;
import com.zlw.audio_recorder.adapter.SettingsTabAdapter;
import com.zlw.audio_recorder.base.MyApp;
import com.zlw.audio_recorder.widget.AudioView;
import com.zlw.loggerlib.Logger;
import com.zlw.main.recorderlib.RecordManager;
import com.zlw.main.recorderlib.recorder.RecordConfig;
import com.zlw.main.recorderlib.recorder.RecordHelper;
import com.zlw.main.recorderlib.recorder.listener.RecordFftDataListener;
import com.zlw.main.recorderlib.recorder.listener.RecordResultListener;
import com.zlw.main.recorderlib.recorder.listener.RecordSoundSizeListener;
import com.zlw.main.recorderlib.recorder.listener.RecordStateListener;

import java.io.File;
import java.util.List;
import java.util.Locale;
import java.util.Timer;
import java.util.TimerTask;
import java.util.UUID;

import com.zlw.audio_recorder.fragment.ExhibitionRoomFragment;
import com.zlw.audio_recorder.model.ExhibitionRoom;
import com.zlw.audio_recorder.network.ApiClient;

public class MainActivity extends AppCompatActivity implements View.OnClickListener {
    private static final String TAG = MainActivity.class.getSimpleName();

    ImageButton btRecord;
    Button btInterrupt;
    AudioView audioView;
    TabLayout tabLayout;
    ViewPager2 viewPager;
    SettingsTabAdapter tabAdapter;

    private boolean isRecording = false;
    private final RecordManager recordManager = RecordManager.getInstance();

    private MediaProjectionManager mediaProjectionManager;

    // 全局sessionId
    private String sessionId;

    // 定时器
    private Timer roomListTimer;

    // 展厅Fragment引用
    private ExhibitionRoomFragment exhibitionRoomFragment;

    // 录音按钮状态枚举
    private enum RecordButtonState {
        STOPPED,    // 停止状态 - 绿色
        RECORDING   // 录音中 - 红色
    }

    @Override
    protected void onCreate(Bundle savedInstanceState) {
        super.onCreate(savedInstanceState);
        setContentView(R.layout.activity_main);

        // 生成随机UUID作为sessionId
        generateSessionId();

        initView();
        initAudioView();
        initEvent();
        initRecord();

        // 设置初始按钮状态为绿色（停止状态）
        updateRecordButtonState(RecordButtonState.STOPPED);

        // 确保Activity可以接收键盘事件
        getWindow().getDecorView().setFocusableInTouchMode(true);
        getWindow().getDecorView().requestFocus();

        // 启动定时任务获取展厅列表
        startRoomListTimer();

        AndPermission.with(this)
                .runtime()
                .permission(new String[]{Permission.READ_EXTERNAL_STORAGE, Permission.WRITE_EXTERNAL_STORAGE,
                        Permission.RECORD_AUDIO})
                .start();
    }

    private void initView() {
        btRecord = findViewById(R.id.btRecord);
        btInterrupt = findViewById(R.id.btInterrupt);
        audioView = findViewById(R.id.audioView);
        tabLayout = findViewById(R.id.tabLayout);
        viewPager = findViewById(R.id.viewPager);

        btRecord.setOnClickListener(this);
        btInterrupt.setOnClickListener(this);

        initTabLayout();
    }

    private void initTabLayout() {
        tabAdapter = new SettingsTabAdapter(this);
        viewPager.setAdapter(tabAdapter);

        new TabLayoutMediator(tabLayout, viewPager, (tab, position) -> {
            switch (position) {
                case 0:
                    tab.setText("选择展厅");
                    break;
                case 1:
                    tab.setText("音频格式");
                    break;
                case 2:
                    tab.setText("音频参数");
                    break;
                case 3:
                    tab.setText("音源选择");
                    break;
            }
        }).attach();
    }

    @Override
    protected void onResume() {
        super.onResume();
        initRecordEvent();
    }

    @Override
    protected void onStop() {
        super.onStop();
    }

    @Override
    protected void onActivityResult(int requestCode, int resultCode, @Nullable Intent data) {
        super.onActivityResult(requestCode, resultCode, data);
        if (resultCode == Activity.RESULT_OK && requestCode == 2000) {
            if (data != null) {
                MediaProjection mediaProjection = mediaProjectionManager.getMediaProjection(resultCode, data);
                recordManager.setMediaProjection(mediaProjection);
            }
        }
    }

    private void initAudioView() {
        // 确保AudioView使用STYLE_HOLLOW_LUMP样式（上半部和下半部都使用相同样式）
        audioView.setStyle(AudioView.ShowStyle.STYLE_HOLLOW_LUMP, AudioView.ShowStyle.STYLE_HOLLOW_LUMP);
    }

    private void initEvent() {
        // Tab相关的事件处理已移至各个Fragment中
    }

    private void initRecord() {
        recordManager.init(MyApp.getInstance(), true);
        recordManager.changeFormat(RecordConfig.RecordFormat.WAV);
        
        // 使用应用内部存储目录
        String recordDir;
        if (Environment.getExternalStorageState().equals(Environment.MEDIA_MOUNTED)) {
            // 外部存储可用
            recordDir = String.format(Locale.getDefault(), "%s/Record/com.zlw.main/",
                    Environment.getExternalStorageDirectory().getAbsolutePath());
        } else {
            // 外部存储不可用，使用应用内部存储
            recordDir = String.format(Locale.getDefault(), "%s/Record/",
                    getFilesDir().getAbsolutePath());
        }
        
        recordManager.changeRecordDir(recordDir);
        initRecordEvent();
    }

    private void initRecordEvent() {
        recordManager.setRecordStateListener(new RecordStateListener() {
            @Override
            public void onStateChange(RecordHelper.RecordState state) {
                Logger.i(TAG, "onStateChange %s", state.name());
                // 状态变化日志记录，不再更新UI显示
            }

            @Override
            public void onError(String error) {
                Logger.i(TAG, "onError %s", error);
            }
        });
        recordManager.setRecordSoundSizeListener(new RecordSoundSizeListener() {
            @Override
            public void onSoundSize(int soundSize) {
                // 声音大小监听，不再更新UI显示
            }
        });
        recordManager.setRecordResultListener(new RecordResultListener() {
            @Override
            public void onResult(File result) {
                String filePath = result.getAbsolutePath();
                String fileName = result.getName();
                long fileSize = result.length();

                Logger.i(TAG, "录音完成 - 文件路径: %s", filePath);
                Logger.i(TAG, "录音完成 - 文件名: %s", fileName);
                Logger.i(TAG, "录音完成 - 文件大小: %s bytes", fileSize);
                Logger.i(TAG, "录音完成 - 当前格式设置: %s", recordManager.getRecordConfig().getFormat().name());

                // 检查文件扩展名
                String extension = "";
                int lastDot = fileName.lastIndexOf('.');
                if (lastDot > 0) {
                    extension = fileName.substring(lastDot);
                }
                Logger.i(TAG, "录音完成 - 文件扩展名: %s", extension);

                String message = String.format("录音文件：%s\n大小：%d bytes\n格式：%s",
                        fileName, fileSize, extension);
                Toast.makeText(MainActivity.this, message, Toast.LENGTH_LONG).show();

                // 录音完成后重置按钮状态为绿色
                updateRecordButtonState(RecordButtonState.STOPPED);
            }
        });
        recordManager.setRecordFftDataListener(new RecordFftDataListener() {
            @Override
            public void onFftData(byte[] data) {
                audioView.setWaveData(data);
            }
        });
    }

    public void onClick(View view) {
        int id = view.getId();
        if (id == R.id.btRecord) {
            toggleRecording();
        } else if (id == R.id.btInterrupt) {
            interruptRecording();
        }
    }

    @Override
    public boolean onKeyDown(int keyCode, KeyEvent event) {
        switch (keyCode) {
            case KeyEvent.KEYCODE_DPAD_UP:      // 方向键上
            case KeyEvent.KEYCODE_PAGE_UP:      // Page Up
                Logger.i(TAG, "键盘快捷键: 开始录音 (方向键上/Page Up)");
                if (!isRecording) {
                    startRecording();
                    Toast.makeText(this, "快捷键开始录音", Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(this, "录音已在进行中", Toast.LENGTH_SHORT).show();
                }
                return true;

            case KeyEvent.KEYCODE_DPAD_DOWN:    // 方向键下
            case KeyEvent.KEYCODE_TAB:          // Tab键
                Logger.i(TAG, "键盘快捷键: 结束录音 (方向键下/Tab)");
                if (isRecording) {
                    stopRecording();
                    Toast.makeText(this, "快捷键结束录音", Toast.LENGTH_SHORT).show();
                } else {
                    Toast.makeText(this, "当前没有录音进行中", Toast.LENGTH_SHORT).show();
                }
                return true;

            case KeyEvent.KEYCODE_PAGE_DOWN:    // Page Down
                Logger.i(TAG, "键盘快捷键: 打断录音 (Page Down)");
                interruptRecording();
                return true;

            default:
                return super.onKeyDown(keyCode, event);
        }
    }

    /**
     * 切换录音状态：开始录音 <-> 停止录音
     */
    private void toggleRecording() {
        if (isRecording) {
            // 当前正在录音，点击停止
            stopRecording();
        } else {
            // 当前未录音，点击开始
            startRecording();
        }
    }

    /**
     * 开始录音
     */
    private void startRecording() {
        RecordConfig config = recordManager.getRecordConfig();
        Logger.i(TAG, "开始录音 - 格式: %s, 采样率: %s, 位宽: %s, 声道: %s",
                config.getFormat().name(),
                config.getSampleRate(),
                config.getEncoding(),
                config.getChannelCount());
        Logger.i(TAG, "开始录音 - 保存目录: %s", config.getRecordDir());

        recordManager.start();
        isRecording = true;
        updateRecordButtonState(RecordButtonState.RECORDING);
    }

    /**
     * 停止录音
     */
    private void stopRecording() {
        Logger.i(TAG, "停止录音");
        recordManager.stop();
        isRecording = false;
        updateRecordButtonState(RecordButtonState.STOPPED);
    }

    /**
     * 打断录音 - 停止录音并调用后台打断接口
     */
    private void interruptRecording() {
        Logger.i(TAG, "用户点击打断按钮");

        // 显示打断中状态
        Toast.makeText(this, "正在执行打断操作...", Toast.LENGTH_SHORT).show();

        // 调用后台打断接口
        ApiClient.interrupt(sessionId, new ApiClient.InterruptCallback() {
            @Override
            public void onSuccess(String message) {
                Logger.i(TAG, "后台打断接口调用成功: %s", message);

                runOnUiThread(() -> {
                    // 停止本地录音
                    if (isRecording) {
                        recordManager.stop();
                        isRecording = false;
                        updateRecordButtonState(RecordButtonState.STOPPED);
                    }

                    Toast.makeText(MainActivity.this, "打断操作成功", Toast.LENGTH_LONG).show();
                });
            }

            @Override
            public void onError(String error) {
                Logger.e(TAG, "后台打断接口调用失败: %s", error);

                runOnUiThread(() -> {
                    // 即使后台接口失败，也停止本地录音
                    if (isRecording) {
                        recordManager.stop();
                        isRecording = false;
                        updateRecordButtonState(RecordButtonState.STOPPED);
                    }

                    Toast.makeText(MainActivity.this, "打断操作失败：" + error, Toast.LENGTH_LONG).show();
                });
            }
        });
    }

    /**
     * 更新录音按钮的状态和颜色
     */
    private void updateRecordButtonState(RecordButtonState state) {
        switch (state) {
            case STOPPED:
                btRecord.setBackgroundResource(R.drawable.record_button_green);
                Logger.i(TAG, "按钮状态: 准备录音 (绿色)");
                break;
            case RECORDING:
                btRecord.setBackgroundResource(R.drawable.record_button_red);
                Logger.i(TAG, "按钮状态: 正在录音 (红色)");
                break;
        }
    }

    /**
     * 生成随机UUID作为sessionId
     */
    private void generateSessionId() {
        sessionId = UUID.randomUUID().toString();
        Logger.i(TAG, "生成sessionId: %s", sessionId);
    }

    /**
     * 启动定时任务获取展厅列表
     */
    private void startRoomListTimer() {
        if (roomListTimer != null) {
            roomListTimer.cancel();
        }

        roomListTimer = new Timer();
        roomListTimer.scheduleAtFixedRate(new TimerTask() {
            @Override
            public void run() {
                fetchRoomList();
            }
        }, 0, 10000); // 立即执行，然后每10秒执行一次

        Logger.i(TAG, "定时任务已启动，每10秒获取展厅列表");
    }

    /**
     * 获取展厅列表
     */
    private void fetchRoomList() {
        ApiClient.getExhibitionRooms(sessionId, new ApiClient.ApiCallback() {
            @Override
            public void onSuccess(List<ExhibitionRoom> rooms) {
                Logger.i(TAG, "获取展厅列表成功，共 %d 个展厅", rooms.size());
                updateExhibitionRoomFragment(rooms);
            }

            @Override
            public void onError(String error) {
                Logger.e(TAG, "获取展厅列表失败: %s", error);
                showExhibitionRoomError(error);
            }
        });
    }

    /**
     * 更新展厅Fragment
     */
    private void updateExhibitionRoomFragment(List<ExhibitionRoom> rooms) {
        runOnUiThread(() -> {
            try {
                // 通过FragmentManager查找Fragment
                Fragment fragment = getSupportFragmentManager().findFragmentByTag("f0");
                if (fragment instanceof ExhibitionRoomFragment) {
                    ExhibitionRoomFragment roomFragment = (ExhibitionRoomFragment) fragment;
                    roomFragment.setSessionId(sessionId); // 设置sessionId
                    roomFragment.updateRoomList(rooms);
                }
            } catch (Exception e) {
                Logger.e(e, TAG, "更新展厅Fragment失败: %s", e.getMessage());
            }
        });
    }

    /**
     * 显示展厅获取错误
     */
    private void showExhibitionRoomError(String error) {
        runOnUiThread(() -> {
            try {
                // 通过FragmentManager查找Fragment
                Fragment fragment = getSupportFragmentManager().findFragmentByTag("f0");
                if (fragment instanceof ExhibitionRoomFragment) {
                    ((ExhibitionRoomFragment) fragment).showError(error);
                }
            } catch (Exception e) {
                Logger.e(e, TAG, "显示展厅错误失败: %s", e.getMessage());
            }
        });
    }

    @Override
    protected void onDestroy() {
        super.onDestroy();
        // 停止定时任务
        if (roomListTimer != null) {
            roomListTimer.cancel();
            roomListTimer = null;
        }
    }
}

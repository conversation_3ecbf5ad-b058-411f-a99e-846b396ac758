<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <!-- 采样率设置 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="采样率"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginBottom="12dp" />

    <RadioGroup
        android:id="@+id/rgSimpleRate"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:layout_marginBottom="24dp">

        <RadioButton
            android:id="@+id/rb8K"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="8 kHz - 电话音质"
            android:textColor="@color/text_secondary"
            android:buttonTint="@color/colorPrimary"
            android:layout_marginBottom="8dp" />

        <RadioButton
            android:id="@+id/rb16K"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:checked="true"
            android:text="16 kHz - 语音录制"
            android:textColor="@color/text_secondary"
            android:buttonTint="@color/colorPrimary"
            android:layout_marginBottom="8dp" />

        <RadioButton
            android:id="@+id/rb44K"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="44.1 kHz - CD音质"
            android:textColor="@color/text_secondary"
            android:buttonTint="@color/colorPrimary" />

    </RadioGroup>

    <!-- 位宽设置 -->
    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="位宽"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginBottom="12dp" />

    <RadioGroup
        android:id="@+id/tbEncoding"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RadioButton
            android:id="@+id/rb8Bit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="8 Bit - 基础音质"
            android:textColor="@color/text_secondary"
            android:buttonTint="@color/colorPrimary"
            android:layout_marginBottom="8dp" />

        <RadioButton
            android:id="@+id/rb16Bit"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:checked="true"
            android:text="16 Bit - 标准音质"
            android:textColor="@color/text_secondary"
            android:buttonTint="@color/colorPrimary" />

    </RadioGroup>

</LinearLayout>

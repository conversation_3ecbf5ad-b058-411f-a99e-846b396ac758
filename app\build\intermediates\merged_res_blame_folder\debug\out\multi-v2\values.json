{"logs": [{"outputFile": "com.zlw.audio_recorder.app-mergeDebugResources-44:/values/values.xml", "map": [{"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\7f55c2808d12cbf97bc3331071c16400\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "335", "startColumns": "4", "startOffsets": "21167", "endColumns": "82", "endOffsets": "21245"}}, {"source": "D:\\Terminus\\ZlwAudioRecorder\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "11,19,28,2", "startColumns": "4,4,4,4", "startOffsets": "488,785,1143,19", "endLines": "16,25,34,8", "endColumns": "12,12,12,12", "endOffsets": "756,1114,1488,461"}, "to": {"startLines": "1456,1672,1734,1843", "startColumns": "4,4,4,4", "startOffsets": "92253,106984,110567,119967", "endLines": "1461,1678,1740,1849", "endColumns": "12,12,12,12", "endOffsets": "92516,107307,110906,120403"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\639cfffdd3e9962f1bdf5d516e9ecbdc\\transformed\\jetified-customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "273,276", "startColumns": "4,4", "startOffsets": "17462,17586", "endColumns": "53,66", "endOffsets": "17511,17648"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\d68cc036a2de5527e7a8088fc46ea908\\transformed\\lifecycle-runtime-2.6.2\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "298", "startColumns": "4", "startOffsets": "18728", "endColumns": "42", "endOffsets": "18766"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\e1ce0c652e870898c214ee480cfa5893\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "300", "startColumns": "4", "startOffsets": "18831", "endColumns": "53", "endOffsets": "18880"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\81290c9d9d4e25e56507b26115115af4\\transformed\\jetified-activity-1.7.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "279,299", "startColumns": "4,4", "startOffsets": "17749,18771", "endColumns": "41,59", "endOffsets": "17786,18826"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\e4060eff540ed441ee42bbd58758cdf0\\transformed\\jetified-permission-2.0.3\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "206,207,208,209,210,211,212,213,214,215,216,217,218,219,352,353,354,355,356,357,358,359,360,361,362,363,1472,1473,1477,1478,1485,1490,1491,1498,1503,1504,1505,1508", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "13686,13734,13782,13830,13878,13924,13972,14020,14068,14114,14162,14210,14258,14306,22133,22219,22281,22343,22401,22472,22534,22600,22656,22721,22773,22833,92939,92970,93150,93187,93542,93816,93907,94310,94598,94636,94731,94854", "endLines": "206,207,208,209,210,211,212,213,214,215,216,217,218,219,352,353,354,355,356,357,358,359,360,361,362,363,1472,1476,1477,1484,1489,1490,1497,1502,1503,1504,1507,1511", "endColumns": "47,47,47,47,45,47,47,47,45,47,47,47,47,47,85,61,61,57,70,61,65,55,64,51,59,73,30,12,36,12,12,90,12,12,37,94,12,12", "endOffsets": "13729,13777,13825,13873,13919,13967,14015,14063,14109,14157,14205,14253,14301,14349,22214,22276,22338,22396,22467,22529,22595,22651,22716,22768,22828,22902,92965,93145,93182,93537,93811,93902,94305,94593,94631,94726,94849,95029"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\cfb6fab0fe63b279fbdaae6be17ab184\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,19,20,21,22,25,26,27,28,29,30,32,33,40,41,42,43,46,47,48,49,52,53,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,80,81,82,83,84,85,87,88,89,90,94,95,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,182,183,184,185,186,187,188,189,190,220,221,222,223,224,225,226,227,263,264,265,266,271,277,278,280,297,303,304,305,306,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,366,384,385,386,387,388,389,397,398,402,406,410,415,421,428,432,436,441,445,449,453,457,461,465,471,475,481,485,491,495,500,504,507,511,517,521,527,531,537,540,544,548,552,556,560,561,562,563,566,569,572,575,579,580,581,582,583,586,588,590,592,597,598,602,608,612,613,615,626,627,631,637,641,642,643,647,674,678,679,683,711,881,907,1078,1104,1135,1143,1149,1163,1185,1190,1195,1205,1214,1223,1227,1234,1242,1249,1250,1259,1262,1265,1269,1273,1277,1280,1281,1286,1291,1301,1306,1313,1319,1320,1323,1327,1332,1334,1336,1339,1342,1344,1348,1351,1358,1361,1364,1368,1370,1374,1376,1378,1380,1384,1392,1400,1412,1418,1427,1430,1441,1444,1445,1450,1451,1512,1581,1651,1652,1662,1671,1679,1681,1685,1688,1691,1694,1697,1700,1703,1706,1710,1713,1716,1719,1723,1726,1730,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1763,1765,1766,1767,1768,1769,1770,1771,1772,1774,1775,1777,1778,1780,1782,1783,1785,1786,1787,1788,1789,1790,1792,1793,1794,1795,1796,1808,1810,1812,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1826,1828,1829,1830,1831,1832,1833,1835,1839,1850,1851,1852,1853,1854,1855,1859,1860,1861,1862,1864,1866,1868,1870,1872,1873,1874,1875,1877,1879,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1895,1896,1897,1898,1900,1902,1903,1905,1906,1908,1910,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1925,1926,1927,1928,1930,1931,1932,1933,1934,1936,1938,1940,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,359,400,455,514,576,657,718,793,869,946,1234,1319,1401,1477,1661,1738,1816,1922,2028,2107,2235,2292,2642,2716,2791,2856,3004,3064,3125,3197,3366,3433,3540,3599,3658,3717,3776,3835,3889,3943,3996,4050,4104,4158,4344,4418,4497,4570,4644,4715,4787,4859,5082,5139,5197,5270,5344,5418,5535,5607,5680,5750,5961,6021,6124,6193,6262,6332,6406,6482,6546,6623,6699,6776,6841,6910,6987,7062,7131,7199,7276,7342,7403,7500,7565,7634,7733,7804,7863,7921,7978,8037,8101,8172,8244,8316,8388,8460,8527,8595,8663,8722,8785,8849,8939,9030,9090,9156,9223,9289,9359,9423,9476,9543,9604,9671,9784,9842,9905,9970,10035,10110,10183,10255,10304,10365,10426,10487,10549,10613,10677,10741,10806,10869,10929,10990,11056,11115,11175,11237,11308,11368,11924,12010,12097,12187,12274,12362,12444,12527,12617,14354,14406,14464,14509,14575,14639,14696,14753,16930,16987,17035,17084,17373,17653,17700,17791,18696,18988,19052,19114,19174,19301,19375,19445,19523,19577,19647,19732,19780,19826,19887,19950,20016,20080,20151,20214,20279,20343,20404,20465,20517,20590,20664,20733,20808,20882,20956,21097,23011,24095,24173,24263,24351,24447,24537,25119,25208,25455,25736,25988,26273,26666,27143,27365,27587,27863,28090,28320,28550,28780,29010,29237,29656,29882,30307,30537,30965,31184,31467,31675,31806,32033,32459,32684,33111,33332,33757,33877,34153,34454,34778,35069,35383,35520,35651,35756,35998,36165,36369,36577,36848,36960,37072,37177,37294,37508,37654,37794,37880,38228,38316,38562,38980,39229,39311,39409,40001,40101,40353,40777,41032,41126,41215,41452,43476,43718,43820,44073,46229,56761,58277,68908,70436,72193,72819,73239,74300,75565,75821,76057,76604,77098,77703,77901,78481,79045,79420,79538,80076,80233,80429,80702,80958,81128,81269,81333,81698,82065,82741,83005,83343,83696,83790,83976,84282,84544,84669,84796,85035,85246,85365,85558,85735,86190,86371,86493,86752,86865,87052,87154,87261,87390,87665,88173,88669,89546,89840,90410,90559,91291,91463,91547,91883,91975,95034,100280,105669,105731,106309,106893,107312,107425,107654,107814,107966,108137,108303,108472,108639,108802,109045,109215,109388,109559,109833,110032,110237,110911,110995,111091,111187,111285,111385,111487,111589,111691,111793,111895,111995,112091,112203,112332,112455,112586,112717,112815,112929,113023,113163,113297,113393,113505,113605,113721,113817,113929,114029,114169,114305,114469,114599,114757,114907,115048,115192,115327,115439,115589,115717,115845,115981,116113,116243,116373,116485,117383,117529,117673,117811,117877,117967,118043,118147,118237,118339,118447,118555,118655,118735,118827,118925,119035,119113,119219,119311,119415,119525,119647,119810,120408,120488,120588,120678,120788,120878,121119,121213,121319,121411,121511,121623,121737,121853,121969,122063,122177,122289,122391,122511,122633,122715,122819,122939,123065,123163,123257,123345,123457,123573,123695,123807,123982,124098,124184,124276,124388,124512,124579,124705,124773,124901,125045,125173,125242,125337,125452,125565,125664,125773,125884,125995,126096,126201,126301,126431,126522,126645,126739,126851,126937,127041,127137,127225,127343,127447,127551,127677,127765,127873,127973,128063,128173,128257,128359,128443,128497,128561,128667,128753,128863,128947", "endLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,19,20,21,22,25,26,27,28,29,30,32,33,40,41,42,43,46,47,48,49,52,53,55,56,57,58,59,60,61,62,63,64,65,66,69,70,71,72,73,74,75,76,80,81,82,83,84,85,87,88,89,90,94,95,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,182,183,184,185,186,187,188,189,190,220,221,222,223,224,225,226,227,263,264,265,266,271,277,278,280,297,303,304,305,306,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,330,331,332,333,334,366,384,385,386,387,388,396,397,401,405,409,414,420,427,431,435,440,444,448,452,456,460,464,470,474,480,484,490,494,499,503,506,510,516,520,526,530,536,539,543,547,551,555,559,560,561,562,565,568,571,574,578,579,580,581,582,585,587,589,591,596,597,601,607,611,612,614,625,626,630,636,640,641,642,646,673,677,678,682,710,880,906,1077,1103,1134,1142,1148,1162,1184,1189,1194,1204,1213,1222,1226,1233,1241,1248,1249,1258,1261,1264,1268,1272,1276,1279,1280,1285,1290,1300,1305,1312,1318,1319,1322,1326,1331,1333,1335,1338,1341,1343,1347,1350,1357,1360,1363,1367,1369,1373,1375,1377,1379,1383,1391,1399,1411,1417,1426,1429,1440,1443,1444,1449,1450,1455,1580,1650,1651,1661,1670,1671,1680,1684,1687,1690,1693,1696,1699,1702,1705,1709,1712,1715,1718,1722,1725,1729,1733,1741,1742,1743,1744,1745,1746,1747,1748,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1762,1764,1765,1766,1767,1768,1769,1770,1771,1773,1774,1776,1777,1779,1781,1782,1784,1785,1786,1787,1788,1789,1791,1792,1793,1794,1795,1796,1809,1811,1813,1814,1815,1816,1817,1818,1819,1820,1821,1822,1823,1824,1825,1827,1828,1829,1830,1831,1832,1834,1838,1842,1850,1851,1852,1853,1854,1858,1859,1860,1861,1863,1865,1867,1869,1871,1872,1873,1874,1876,1878,1880,1881,1882,1883,1884,1885,1886,1887,1888,1889,1890,1891,1894,1895,1896,1897,1899,1901,1902,1904,1905,1907,1909,1911,1912,1913,1914,1915,1916,1917,1918,1919,1920,1921,1922,1924,1925,1926,1927,1929,1930,1931,1932,1933,1935,1937,1939,1941,1942,1943,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "200,245,294,395,450,509,571,652,713,788,864,941,1019,1314,1396,1472,1548,1733,1811,1917,2023,2102,2182,2287,2345,2711,2786,2851,2917,3059,3120,3192,3265,3428,3496,3594,3653,3712,3771,3830,3884,3938,3991,4045,4099,4153,4207,4413,4492,4565,4639,4710,4782,4854,4927,5134,5192,5265,5339,5413,5488,5602,5675,5745,5816,6016,6077,6188,6257,6327,6401,6477,6541,6618,6694,6771,6836,6905,6982,7057,7126,7194,7271,7337,7398,7495,7560,7629,7728,7799,7858,7916,7973,8032,8096,8167,8239,8311,8383,8455,8522,8590,8658,8717,8780,8844,8934,9025,9085,9151,9218,9284,9354,9418,9471,9538,9599,9666,9779,9837,9900,9965,10030,10105,10178,10250,10299,10360,10421,10482,10544,10608,10672,10736,10801,10864,10924,10985,11051,11110,11170,11232,11303,11363,11431,12005,12092,12182,12269,12357,12439,12522,12612,12703,14401,14459,14504,14570,14634,14691,14748,14802,16982,17030,17079,17130,17402,17695,17744,17832,18723,19047,19109,19169,19226,19370,19440,19518,19572,19642,19727,19775,19821,19882,19945,20011,20075,20146,20209,20274,20338,20399,20460,20512,20585,20659,20728,20803,20877,20951,21092,21162,23059,24168,24258,24346,24442,24532,25114,25203,25450,25731,25983,26268,26661,27138,27360,27582,27858,28085,28315,28545,28775,29005,29232,29651,29877,30302,30532,30960,31179,31462,31670,31801,32028,32454,32679,33106,33327,33752,33872,34148,34449,34773,35064,35378,35515,35646,35751,35993,36160,36364,36572,36843,36955,37067,37172,37289,37503,37649,37789,37875,38223,38311,38557,38975,39224,39306,39404,39996,40096,40348,40772,41027,41121,41210,41447,43471,43713,43815,44068,46224,56756,58272,68903,70431,72188,72814,73234,74295,75560,75816,76052,76599,77093,77698,77896,78476,79040,79415,79533,80071,80228,80424,80697,80953,81123,81264,81328,81693,82060,82736,83000,83338,83691,83785,83971,84277,84539,84664,84791,85030,85241,85360,85553,85730,86185,86366,86488,86747,86860,87047,87149,87256,87385,87660,88168,88664,89541,89835,90405,90554,91286,91458,91542,91878,91970,92248,100275,105664,105726,106304,106888,106979,107420,107649,107809,107961,108132,108298,108467,108634,108797,109040,109210,109383,109554,109828,110027,110232,110562,110990,111086,111182,111280,111380,111482,111584,111686,111788,111890,111990,112086,112198,112327,112450,112581,112712,112810,112924,113018,113158,113292,113388,113500,113600,113716,113812,113924,114024,114164,114300,114464,114594,114752,114902,115043,115187,115322,115434,115584,115712,115840,115976,116108,116238,116368,116480,116620,117524,117668,117806,117872,117962,118038,118142,118232,118334,118442,118550,118650,118730,118822,118920,119030,119108,119214,119306,119410,119520,119642,119805,119962,120483,120583,120673,120783,120873,121114,121208,121314,121406,121506,121618,121732,121848,121964,122058,122172,122284,122386,122506,122628,122710,122814,122934,123060,123158,123252,123340,123452,123568,123690,123802,123977,124093,124179,124271,124383,124507,124574,124700,124768,124896,125040,125168,125237,125332,125447,125560,125659,125768,125879,125990,126091,126196,126296,126426,126517,126640,126734,126846,126932,127036,127132,127220,127338,127442,127546,127672,127760,127868,127968,128058,128168,128252,128354,128438,128492,128556,128662,128748,128858,128942,129062"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\c29cd734529d008ae2da02db61473f9d\\transformed\\lifecycle-viewmodel-2.6.2\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "301", "startColumns": "4", "startOffsets": "18885", "endColumns": "49", "endOffsets": "18930"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\c7c7cbb5ee8217bc10c58177f979464a\\transformed\\jetified-ui-1.4.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,267,268,269,270,272,302,338,339,341,342,344,346,347,348,349,350,351,364,365,367,369,370,371,1462,1465,1468", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "15020,15079,15138,15198,15258,15318,15378,15438,15498,15558,15618,15678,15738,15797,15857,15917,15977,16037,16097,16157,16217,16277,16337,16397,16456,16516,16576,16635,16694,16753,16812,16871,17135,17209,17267,17322,17407,18935,21353,21418,21520,21586,21731,21835,21887,21947,22009,22063,22099,22907,22957,23064,23181,23228,23264,92521,92633,92744", "endLines": "231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,255,256,257,258,259,260,261,262,267,268,269,270,272,302,338,339,341,342,344,346,347,348,349,350,351,364,365,367,369,370,371,1464,1467,1471", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "15074,15133,15193,15253,15313,15373,15433,15493,15553,15613,15673,15733,15792,15852,15912,15972,16032,16092,16152,16212,16272,16332,16392,16451,16511,16571,16630,16689,16748,16807,16866,16925,17204,17262,17317,17368,17457,18983,21413,21467,21581,21682,21784,21882,21942,22004,22058,22094,22128,22952,23006,23105,23223,23259,23349,92628,92739,92934"}}, {"source": "D:\\Terminus\\ZlwAudioRecorder\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "14,12,13,33,31,32,28,5,3,4,27,38,9,8,39,22,23,24,36,19,17,18,37", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "477,367,421,1106,1004,1054,934,182,82,130,891,1262,299,249,1303,715,767,818,1176,647,549,597,1219", "endColumns": "49,52,54,47,48,50,47,45,46,50,41,39,46,48,38,50,49,48,41,43,46,48,41", "endOffsets": "522,415,471,1149,1048,1100,977,223,124,176,928,1297,341,293,1337,761,812,862,1213,686,591,641,1256"}, "to": {"startLines": "18,23,24,31,34,35,36,37,38,39,44,45,50,51,54,77,78,79,86,91,92,93,96", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "1184,1553,1606,2187,2350,2399,2450,2498,2544,2591,2922,2964,3270,3317,3501,4932,4983,5033,5493,5821,5865,5912,6082", "endColumns": "49,52,54,47,48,50,47,45,46,50,41,39,46,48,38,50,49,48,41,43,46,48,41", "endOffsets": "1229,1601,1656,2230,2394,2445,2493,2539,2586,2637,2959,2999,3312,3361,3535,4978,5028,5077,5530,5860,5907,5956,6119"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\7ba74c55d38703961fa899c39b0cb95d\\transformed\\core-1.9.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "5,16,17,67,68,175,176,177,178,179,180,181,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,228,229,230,274,275,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,307,368,1797,1798,1802,1803,1807,1957,1958", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,1024,1096,4212,4281,11436,11506,11574,11646,11716,11777,11851,12708,12769,12830,12892,12956,13018,13079,13147,13247,13307,13373,13446,13515,13572,13624,14807,14879,14955,17516,17551,17837,17892,17955,18010,18068,18126,18187,18250,18307,18358,18408,18469,18526,18592,18626,18661,19231,23110,116625,116742,116943,117053,117254,129067,129139", "endLines": "5,16,17,67,68,175,176,177,178,179,180,181,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,228,229,230,274,275,281,282,283,284,285,286,287,288,289,290,291,292,293,294,295,296,307,368,1797,1801,1802,1806,1807,1957,1958", "endColumns": "59,71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,70,116,12,109,12,128,71,66", "endOffsets": "354,1091,1179,4276,4339,11501,11569,11641,11711,11772,11846,11919,12764,12825,12887,12951,13013,13074,13142,13242,13302,13368,13441,13510,13567,13619,13681,14874,14950,15015,17546,17581,17887,17950,18005,18063,18121,18182,18245,18302,18353,18403,18464,18521,18587,18621,18656,18691,19296,23176,116737,116938,117048,117249,117378,129134,129201"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\92ca04b3920ec15624bc0971373881d7\\transformed\\jetified-utilcodex-1.31.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "372", "startColumns": "4", "startOffsets": "23354", "endLines": "383", "endColumns": "12", "endOffsets": "24090"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\2f11cd048c7739820e5cda721fe9c715\\transformed\\jetified-material3-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1", "startColumns": "-1,-1,-1", "startOffsets": "-1,-1,-1"}, "to": {"startLines": "340,343,345", "startColumns": "4,4,4", "startOffsets": "21472,21687,21789", "endColumns": "47,43,45", "endOffsets": "21515,21726,21830"}}, {"source": "D:\\Terminus\\ZlwAudioRecorder\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1,2", "startColumns": "4,4", "startOffsets": "17,62", "endColumns": "43,58", "endOffsets": "56,116"}, "to": {"startLines": "336,337", "startColumns": "4,4", "startOffsets": "21250,21294", "endColumns": "43,58", "endOffsets": "21289,21348"}}]}, {"outputFile": "D:\\AndroidStudio\\.gradle\\daemon\\8.12\\com.zlw.audio_recorder.app-mergeDebugResources-44:\\values\\values.xml", "map": [{"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\7f55c2808d12cbf97bc3331071c16400\\transformed\\jetified-startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "315", "startColumns": "4", "startOffsets": "20222", "endColumns": "82", "endOffsets": "20300"}}, {"source": "D:\\Terminus\\ZlwAudioRecorder\\app\\src\\main\\res\\values\\styles.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "19", "endColumns": "86", "endOffsets": "101"}, "to": {"startLines": "1802", "startColumns": "4", "startOffsets": "118033", "endColumns": "85", "endOffsets": "118114"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\639cfffdd3e9962f1bdf5d516e9ecbdc\\transformed\\jetified-customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "253,256", "startColumns": "4,4", "startOffsets": "16517,16641", "endColumns": "53,66", "endOffsets": "16566,16703"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\d68cc036a2de5527e7a8088fc46ea908\\transformed\\lifecycle-runtime-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "278", "startColumns": "4", "startOffsets": "17783", "endColumns": "42", "endOffsets": "17821"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\e1ce0c652e870898c214ee480cfa5893\\transformed\\jetified-savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "280", "startColumns": "4", "startOffsets": "17886", "endColumns": "53", "endOffsets": "17935"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\81290c9d9d4e25e56507b26115115af4\\transformed\\jetified-activity-1.7.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "259,279", "startColumns": "4,4", "startOffsets": "16804,17826", "endColumns": "41,59", "endOffsets": "16841,17881"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\e4060eff540ed441ee42bbd58758cdf0\\transformed\\jetified-permission-2.0.3\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,33,34,41,46,47,54,59,60,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,103,151,199,247,293,341,389,437,483,531,579,627,675,723,809,871,933,991,1062,1124,1190,1246,1311,1363,1423,1497,1528,1708,1745,2100,2374,2465,2868,3156,3194,3289,3412", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,32,33,40,45,46,53,58,59,60,63,67", "endColumns": "47,47,47,47,45,47,47,47,45,47,47,47,47,47,85,61,61,57,70,61,65,55,64,51,59,73,30,12,36,12,12,90,12,12,37,94,12,12", "endOffsets": "98,146,194,242,288,336,384,432,478,526,574,622,670,718,804,866,928,986,1057,1119,1185,1241,1306,1358,1418,1492,1523,1703,1740,2095,2369,2460,2863,3151,3189,3284,3407,3587"}, "to": {"startLines": "186,187,188,189,190,191,192,193,194,195,196,197,198,199,331,332,333,334,335,336,337,338,339,340,341,342,1445,1446,1450,1451,1458,1463,1464,1471,1476,1477,1478,1481", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "12741,12789,12837,12885,12933,12979,13027,13075,13123,13169,13217,13265,13313,13361,21139,21225,21287,21349,21407,21478,21540,21606,21662,21727,21779,21839,91677,91708,91888,91925,92280,92554,92645,93048,93336,93374,93469,93592", "endLines": "186,187,188,189,190,191,192,193,194,195,196,197,198,199,331,332,333,334,335,336,337,338,339,340,341,342,1445,1449,1450,1457,1462,1463,1470,1475,1476,1477,1480,1484", "endColumns": "47,47,47,47,45,47,47,47,45,47,47,47,47,47,85,61,61,57,70,61,65,55,64,51,59,73,30,12,36,12,12,90,12,12,37,94,12,12", "endOffsets": "12784,12832,12880,12928,12974,13022,13070,13118,13164,13212,13260,13308,13356,13404,21220,21282,21344,21402,21473,21535,21601,21657,21722,21774,21834,21908,91703,91883,91920,92275,92549,92640,93043,93331,93369,93464,93587,93767"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\cfb6fab0fe63b279fbdaae6be17ab184\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,20,21,22,23,24,25,26,27,28,29,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,200,201,202,203,204,205,206,207,243,244,245,246,251,257,258,260,277,283,284,285,286,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,345,363,364,365,366,367,368,376,377,381,385,389,394,400,407,411,415,420,424,428,432,436,440,444,450,454,460,464,470,474,479,483,486,490,496,500,506,510,516,519,523,527,531,535,539,540,541,542,545,548,551,554,558,559,560,561,562,565,567,569,571,576,577,581,587,591,592,594,605,606,610,616,620,621,622,626,653,657,658,662,690,860,886,1057,1083,1114,1122,1128,1142,1164,1169,1174,1184,1193,1202,1206,1213,1221,1228,1229,1238,1241,1244,1248,1252,1256,1259,1260,1265,1270,1280,1285,1292,1298,1299,1302,1306,1311,1313,1315,1318,1321,1323,1327,1330,1337,1340,1343,1347,1349,1353,1355,1357,1359,1363,1371,1379,1391,1397,1406,1409,1420,1423,1424,1429,1430,1485,1554,1624,1625,1635,1644,1645,1647,1651,1654,1657,1660,1663,1666,1669,1672,1676,1679,1682,1685,1689,1692,1696,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1720,1722,1724,1725,1726,1727,1728,1729,1730,1731,1733,1734,1736,1737,1739,1741,1742,1744,1745,1746,1747,1748,1749,1751,1752,1753,1754,1755,1767,1769,1771,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1785,1787,1788,1789,1790,1791,1792,1794,1798,1803,1804,1805,1806,1807,1808,1812,1813,1814,1815,1817,1819,1821,1823,1825,1826,1827,1828,1830,1832,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1845,1848,1849,1850,1851,1853,1855,1856,1858,1859,1861,1863,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1876,1878,1879,1880,1881,1883,1884,1885,1886,1887,1889,1891,1893,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1912,1987,1990,1993,1996,2010,2016,2058,2087,2114,2123,2185,2544,2564,2592,2703,2727,2733,2739,2760,2884,2904,2910,2914,2920,2955,2987,3053,3073,3128,3140,3166", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,250,359,400,455,514,576,657,718,793,869,946,1184,1269,1351,1427,1503,1580,1658,1764,1870,1949,2029,2086,2288,2362,2437,2502,2568,2628,2689,2761,2834,2901,2969,3028,3087,3146,3205,3264,3318,3372,3425,3479,3533,3587,3773,3847,3926,3999,4073,4144,4216,4288,4361,4418,4476,4549,4623,4697,4772,4844,4917,4987,5058,5118,5179,5248,5317,5387,5461,5537,5601,5678,5754,5831,5896,5965,6042,6117,6186,6254,6331,6397,6458,6555,6620,6689,6788,6859,6918,6976,7033,7092,7156,7227,7299,7371,7443,7515,7582,7650,7718,7777,7840,7904,7994,8085,8145,8211,8278,8344,8414,8478,8531,8598,8659,8726,8839,8897,8960,9025,9090,9165,9238,9310,9359,9420,9481,9542,9604,9668,9732,9796,9861,9924,9984,10045,10111,10170,10230,10292,10363,10423,10979,11065,11152,11242,11329,11417,11499,11582,11672,13409,13461,13519,13564,13630,13694,13751,13808,15985,16042,16090,16139,16428,16708,16755,16846,17751,18043,18107,18169,18229,18356,18430,18500,18578,18632,18702,18787,18835,18881,18942,19005,19071,19135,19206,19269,19334,19398,19459,19520,19572,19645,19719,19788,19863,19937,20011,20152,22017,23101,23179,23269,23357,23453,23543,24125,24214,24461,24742,24994,25279,25672,26149,26371,26593,26869,27096,27326,27556,27786,28016,28243,28662,28888,29313,29543,29971,30190,30473,30681,30812,31039,31465,31690,32117,32338,32763,32883,33159,33460,33784,34075,34389,34526,34657,34762,35004,35171,35375,35583,35854,35966,36078,36183,36300,36514,36660,36800,36886,37234,37322,37568,37986,38235,38317,38415,39007,39107,39359,39783,40038,40132,40221,40458,42482,42724,42826,43079,45235,55767,57283,67914,69442,71199,71825,72245,73306,74571,74827,75063,75610,76104,76709,76907,77487,78051,78426,78544,79082,79239,79435,79708,79964,80134,80275,80339,80704,81071,81747,82011,82349,82702,82796,82982,83288,83550,83675,83802,84041,84252,84371,84564,84741,85196,85377,85499,85758,85871,86058,86160,86267,86396,86671,87179,87675,88552,88846,89416,89565,90297,90469,90553,90889,90981,93772,99018,104407,104469,105047,105631,105722,105835,106064,106224,106376,106547,106713,106882,107049,107212,107455,107625,107798,107969,108243,108442,108647,108977,109061,109157,109253,109351,109451,109553,109655,109757,109859,109961,110061,110157,110269,110398,110521,110652,110783,110881,110995,111089,111229,111363,111459,111571,111671,111787,111883,111995,112095,112235,112371,112535,112665,112823,112973,113114,113258,113393,113505,113655,113783,113911,114047,114179,114309,114439,114551,115449,115595,115739,115877,115943,116033,116109,116213,116303,116405,116513,116621,116721,116801,116893,116991,117101,117179,117285,117377,117481,117591,117713,117876,118119,118199,118299,118389,118499,118589,118830,118924,119030,119122,119222,119334,119448,119564,119680,119774,119888,120000,120102,120222,120344,120426,120530,120650,120776,120874,120968,121056,121168,121284,121406,121518,121693,121809,121895,121987,122099,122223,122290,122416,122484,122612,122756,122884,122953,123048,123163,123276,123375,123484,123595,123706,123807,123912,124012,124142,124233,124356,124450,124562,124648,124752,124848,124936,125054,125158,125262,125388,125476,125584,125684,125774,125884,125968,126070,126154,126208,126272,126378,126464,126574,126658,126917,129533,129651,129766,129846,130207,130440,131844,133188,134549,134937,137712,147616,148255,149612,153445,154196,154458,154658,155037,159315,159921,160150,160301,160516,161599,162449,165475,166219,168350,168690,170001", "endLines": "2,3,4,6,7,8,9,10,11,12,13,14,15,18,19,20,21,22,23,24,25,26,27,28,29,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,200,201,202,203,204,205,206,207,243,244,245,246,251,257,258,260,277,283,284,285,286,288,289,290,291,292,293,294,295,296,297,298,299,300,301,302,303,304,305,306,307,308,309,310,311,312,313,314,345,363,364,365,366,367,375,376,380,384,388,393,399,406,410,414,419,423,427,431,435,439,443,449,453,459,463,469,473,478,482,485,489,495,499,505,509,515,518,522,526,530,534,538,539,540,541,544,547,550,553,557,558,559,560,561,564,566,568,570,575,576,580,586,590,591,593,604,605,609,615,619,620,621,625,652,656,657,661,689,859,885,1056,1082,1113,1121,1127,1141,1163,1168,1173,1183,1192,1201,1205,1212,1220,1227,1228,1237,1240,1243,1247,1251,1255,1258,1259,1264,1269,1279,1284,1291,1297,1298,1301,1305,1310,1312,1314,1317,1320,1322,1326,1329,1336,1339,1342,1346,1348,1352,1354,1356,1358,1362,1370,1378,1390,1396,1405,1408,1419,1422,1423,1428,1429,1434,1553,1623,1624,1634,1643,1644,1646,1650,1653,1656,1659,1662,1665,1668,1671,1675,1678,1681,1684,1688,1691,1695,1699,1700,1701,1702,1703,1704,1705,1706,1707,1708,1709,1710,1711,1712,1713,1714,1715,1716,1717,1718,1719,1721,1723,1724,1725,1726,1727,1728,1729,1730,1732,1733,1735,1736,1738,1740,1741,1743,1744,1745,1746,1747,1748,1750,1751,1752,1753,1754,1755,1768,1770,1772,1773,1774,1775,1776,1777,1778,1779,1780,1781,1782,1783,1784,1786,1787,1788,1789,1790,1791,1793,1797,1801,1803,1804,1805,1806,1807,1811,1812,1813,1814,1816,1818,1820,1822,1824,1825,1826,1827,1829,1831,1833,1834,1835,1836,1837,1838,1839,1840,1841,1842,1843,1844,1847,1848,1849,1850,1852,1854,1855,1857,1858,1860,1862,1864,1865,1866,1867,1868,1869,1870,1871,1872,1873,1874,1875,1877,1878,1879,1880,1882,1883,1884,1885,1886,1888,1890,1892,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1905,1906,1907,1908,1909,1986,1989,1992,1995,2009,2015,2025,2086,2113,2122,2184,2543,2547,2591,2609,2726,2732,2738,2759,2883,2903,2909,2913,2919,2954,2966,3052,3072,3127,3139,3165,3172", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,294,395,450,509,571,652,713,788,864,941,1019,1264,1346,1422,1498,1575,1653,1759,1865,1944,2024,2081,2139,2357,2432,2497,2563,2623,2684,2756,2829,2896,2964,3023,3082,3141,3200,3259,3313,3367,3420,3474,3528,3582,3636,3842,3921,3994,4068,4139,4211,4283,4356,4413,4471,4544,4618,4692,4767,4839,4912,4982,5053,5113,5174,5243,5312,5382,5456,5532,5596,5673,5749,5826,5891,5960,6037,6112,6181,6249,6326,6392,6453,6550,6615,6684,6783,6854,6913,6971,7028,7087,7151,7222,7294,7366,7438,7510,7577,7645,7713,7772,7835,7899,7989,8080,8140,8206,8273,8339,8409,8473,8526,8593,8654,8721,8834,8892,8955,9020,9085,9160,9233,9305,9354,9415,9476,9537,9599,9663,9727,9791,9856,9919,9979,10040,10106,10165,10225,10287,10358,10418,10486,11060,11147,11237,11324,11412,11494,11577,11667,11758,13456,13514,13559,13625,13689,13746,13803,13857,16037,16085,16134,16185,16457,16750,16799,16887,17778,18102,18164,18224,18281,18425,18495,18573,18627,18697,18782,18830,18876,18937,19000,19066,19130,19201,19264,19329,19393,19454,19515,19567,19640,19714,19783,19858,19932,20006,20147,20217,22065,23174,23264,23352,23448,23538,24120,24209,24456,24737,24989,25274,25667,26144,26366,26588,26864,27091,27321,27551,27781,28011,28238,28657,28883,29308,29538,29966,30185,30468,30676,30807,31034,31460,31685,32112,32333,32758,32878,33154,33455,33779,34070,34384,34521,34652,34757,34999,35166,35370,35578,35849,35961,36073,36178,36295,36509,36655,36795,36881,37229,37317,37563,37981,38230,38312,38410,39002,39102,39354,39778,40033,40127,40216,40453,42477,42719,42821,43074,45230,55762,57278,67909,69437,71194,71820,72240,73301,74566,74822,75058,75605,76099,76704,76902,77482,78046,78421,78539,79077,79234,79430,79703,79959,80129,80270,80334,80699,81066,81742,82006,82344,82697,82791,82977,83283,83545,83670,83797,84036,84247,84366,84559,84736,85191,85372,85494,85753,85866,86053,86155,86262,86391,86666,87174,87670,88547,88841,89411,89560,90292,90464,90548,90884,90976,91254,99013,104402,104464,105042,105626,105717,105830,106059,106219,106371,106542,106708,106877,107044,107207,107450,107620,107793,107964,108238,108437,108642,108972,109056,109152,109248,109346,109446,109548,109650,109752,109854,109956,110056,110152,110264,110393,110516,110647,110778,110876,110990,111084,111224,111358,111454,111566,111666,111782,111878,111990,112090,112230,112366,112530,112660,112818,112968,113109,113253,113388,113500,113650,113778,113906,114042,114174,114304,114434,114546,114686,115590,115734,115872,115938,116028,116104,116208,116298,116400,116508,116616,116716,116796,116888,116986,117096,117174,117280,117372,117476,117586,117708,117871,118028,118194,118294,118384,118494,118584,118825,118919,119025,119117,119217,119329,119443,119559,119675,119769,119883,119995,120097,120217,120339,120421,120525,120645,120771,120869,120963,121051,121163,121279,121401,121513,121688,121804,121890,121982,122094,122218,122285,122411,122479,122607,122751,122879,122948,123043,123158,123271,123370,123479,123590,123701,123802,123907,124007,124137,124228,124351,124445,124557,124643,124747,124843,124931,125049,125153,125257,125383,125471,125579,125679,125769,125879,125963,126065,126149,126203,126267,126373,126459,126569,126653,126773,129528,129646,129761,129841,130202,130435,130952,133183,134544,134932,137707,147611,147746,149607,150179,154191,154453,154653,155032,159310,159916,160145,160296,160511,161594,161906,165470,166214,168345,168685,169996,170199"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\c29cd734529d008ae2da02db61473f9d\\transformed\\lifecycle-viewmodel-2.6.2\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "281", "startColumns": "4", "startOffsets": "17940", "endColumns": "49", "endOffsets": "17985"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\c7c7cbb5ee8217bc10c58177f979464a\\transformed\\jetified-ui-1.4.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3014,3048,3098,3152,3198,3245,3281,3371,3483,3594", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,59,62,66", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3009,3043,3093,3147,3193,3240,3276,3366,3478,3589,3784"}, "to": {"startLines": "211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,247,248,249,250,252,282,317,318,320,321,323,325,326,327,328,329,330,343,344,346,348,349,350,1435,1438,1441", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14075,14134,14193,14253,14313,14373,14433,14493,14553,14613,14673,14733,14793,14852,14912,14972,15032,15092,15152,15212,15272,15332,15392,15452,15511,15571,15631,15690,15749,15808,15867,15926,16190,16264,16322,16377,16462,17990,20359,20424,20526,20592,20737,20841,20893,20953,21015,21069,21105,21913,21963,22070,22187,22234,22270,91259,91371,91482", "endLines": "211,212,213,214,215,216,217,218,219,220,221,222,223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,247,248,249,250,252,282,317,318,320,321,323,325,326,327,328,329,330,343,344,346,348,349,350,1437,1440,1444", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,35,33,49,53,45,46,35,89,12,12,12", "endOffsets": "14129,14188,14248,14308,14368,14428,14488,14548,14608,14668,14728,14788,14847,14907,14967,15027,15087,15147,15207,15267,15327,15387,15447,15506,15566,15626,15685,15744,15803,15862,15921,15980,16259,16317,16372,16423,16512,18038,20419,20473,20587,20688,20790,20888,20948,21010,21064,21100,21134,21958,22012,22111,22229,22265,22355,91366,91477,91672"}}, {"source": "D:\\Terminus\\ZlwAudioRecorder\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "4,2,3", "startColumns": "4,4,4", "startOffsets": "157,57,105", "endColumns": "45,46,50", "endOffsets": "198,99,151"}, "to": {"startLines": "30,31,32", "startColumns": "4,4,4", "startOffsets": "2144,2190,2237", "endColumns": "45,46,50", "endOffsets": "2185,2232,2283"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\7ba74c55d38703961fa899c39b0cb95d\\transformed\\core-1.9.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,89,90,94,95,96,97,103,113,146,167,200", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,344,407,477,545,617,687,748,822,895,956,1017,1079,1143,1205,1266,1334,1434,1494,1560,1633,1702,1759,1811,1873,1945,2021,3996,4031,4066,4121,4184,4239,4297,4355,4416,4479,4536,4587,4637,4698,4755,4821,4855,4890,4925,4995,5066,5183,5384,5494,5695,5824,5896,5963,6166,6467,8198,8879,9561", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,88,89,93,94,95,96,102,112,145,166,199,205", "endColumns": "59,71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,339,402,472,540,612,682,743,817,890,951,1012,1074,1138,1200,1261,1329,1429,1489,1555,1628,1697,1754,1806,1868,1940,2016,2081,4026,4061,4116,4179,4234,4292,4350,4411,4474,4531,4582,4632,4693,4750,4816,4850,4885,4920,4990,5061,5178,5379,5489,5690,5819,5891,5958,6161,6462,8193,8874,9556,9723"}, "to": {"startLines": "5,16,17,55,56,155,156,157,158,159,160,161,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,208,209,210,254,255,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,287,347,1756,1757,1761,1762,1766,1910,1911,2548,2554,2610,2643,2664,2697", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "299,1024,1096,3641,3710,10491,10561,10629,10701,10771,10832,10906,11763,11824,11885,11947,12011,12073,12134,12202,12302,12362,12428,12501,12570,12627,12679,13862,13934,14010,16571,16606,16892,16947,17010,17065,17123,17181,17242,17305,17362,17413,17463,17524,17581,17647,17681,17716,18286,22116,114691,114808,115009,115119,115320,126778,126850,147751,147954,150184,151915,152596,153278", "endLines": "5,16,17,55,56,155,156,157,158,159,160,161,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,208,209,210,254,255,261,262,263,264,265,266,267,268,269,270,271,272,273,274,275,276,287,347,1756,1760,1761,1765,1766,1910,1911,2553,2563,2642,2663,2696,2702", "endColumns": "59,71,87,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "354,1091,1179,3705,3768,10556,10624,10696,10766,10827,10901,10974,11819,11880,11942,12006,12068,12129,12197,12297,12357,12423,12496,12565,12622,12674,12736,13929,14005,14070,16601,16636,16942,17005,17060,17118,17176,17237,17300,17357,17408,17458,17519,17576,17642,17676,17711,17746,18351,22182,114803,115004,115114,115315,115444,126845,126912,147949,148250,151910,152591,153273,153440"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\e4d55a293d403b9cf73bd36431b123f6\\transformed\\jetified-appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2026,2042,2048,2967,2983", "startColumns": "4,4,4,4,4", "startOffsets": "130957,131382,131560,161911,162322", "endLines": "2041,2047,2057,2982,2986", "endColumns": "24,24,24,24,24", "endOffsets": "131377,131555,131839,162317,162444"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\92ca04b3920ec15624bc0971373881d7\\transformed\\jetified-utilcodex-1.31.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endLines": "13", "endColumns": "12", "endOffsets": "791"}, "to": {"startLines": "351", "startColumns": "4", "startOffsets": "22360", "endLines": "362", "endColumns": "12", "endOffsets": "23096"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\2f11cd048c7739820e5cda721fe9c715\\transformed\\jetified-material3-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,103,147", "endColumns": "47,43,45", "endOffsets": "98,142,188"}, "to": {"startLines": "319,322,324", "startColumns": "4,4,4", "startOffsets": "20478,20693,20795", "endColumns": "47,43,45", "endOffsets": "20521,20732,20836"}}, {"source": "D:\\Terminus\\ZlwAudioRecorder\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "17", "endColumns": "53", "endOffsets": "66"}, "to": {"startLines": "316", "startColumns": "4", "startOffsets": "20305", "endColumns": "53", "endOffsets": "20354"}}]}]}
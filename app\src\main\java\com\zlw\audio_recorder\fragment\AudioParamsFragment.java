package com.zlw.audio_recorder.fragment;

import android.media.AudioFormat;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.zlw.audio_recorder.R;
import com.zlw.main.recorderlib.RecordManager;

public class AudioParamsFragment extends Fragment {

    private RadioGroup rgSimpleRate;
    private RadioGroup tbEncoding;
    private RecordManager recordManager;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.tab_audio_params, container, false);
        
        rgSimpleRate = view.findViewById(R.id.rgSimpleRate);
        tbEncoding = view.findViewById(R.id.tbEncoding);
        recordManager = RecordManager.getInstance();
        
        initEvent();
        
        return view;
    }

    private void initEvent() {
        rgSimpleRate.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                if (checkedId == R.id.rb8K) {
                    recordManager.changeRecordConfig(recordManager.getRecordConfig().setSampleRate(8000));
                } else if (checkedId == R.id.rb16K) {
                    recordManager.changeRecordConfig(recordManager.getRecordConfig().setSampleRate(16000));
                } else if (checkedId == R.id.rb44K) {
                    recordManager.changeRecordConfig(recordManager.getRecordConfig().setSampleRate(44100));
                }
            }
        });

        tbEncoding.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                if (checkedId == R.id.rb8Bit) {
                    recordManager.changeRecordConfig(recordManager.getRecordConfig().setEncodingConfig(AudioFormat.ENCODING_PCM_8BIT));
                } else if (checkedId == R.id.rb16Bit) {
                    recordManager.changeRecordConfig(recordManager.getRecordConfig().setEncodingConfig(AudioFormat.ENCODING_PCM_16BIT));
                }
            }
        });
    }
}

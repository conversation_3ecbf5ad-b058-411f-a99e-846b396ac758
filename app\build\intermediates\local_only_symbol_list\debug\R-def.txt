R_DEF: Internal format may change without notice
local
color background_card
color background_primary
color background_secondary
color button_danger
color button_primary
color button_secondary
color card_shadow
color colorAccent
color colorPrimary
color colorPrimaryDark
color divider
color error
color gradient_end
color gradient_start
color info
color recording_active
color recording_pause
color recording_stop
color success
color text_hint
color text_primary
color text_secondary
color warning
drawable button_danger
drawable button_primary
drawable button_secondary
drawable card_background
drawable gradient_background
drawable ic_launcher_background
drawable ic_launcher_foreground
drawable ic_launcher_foreground_new
drawable ic_microphone
drawable ic_pause
drawable ic_stop
drawable record_button
drawable splash_background
drawable stop_button
id audioView
id btRecord
id btStop
id jumpTestActivity
id rb16Bit
id rb16K
id rb44K
id rb8Bit
id rb8K
id rbMic
id rbMp3
id rbPcm
id rbSystem
id rbWav
id rgAudioFormat
id rgSimpleRate
id spDownStyle
id spUpStyle
id tbEncoding
id tbSource
id tvSoundSize
id tvState
layout activity_hz
layout activity_main
mipmap ic_launcher
mipmap ic_launcher_round
string app_name
string app_name_en
style CardStyle
style PrimaryButtonStyle
style SecondaryButtonStyle
style Theme.ZlwAudioRecorder

<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Terminus\ZlwAudioRecorder\recorderlib\libs"><file name="arm64-v8a/libmp3lame.so" path="D:\Terminus\ZlwAudioRecorder\recorderlib\libs\arm64-v8a\libmp3lame.so"/><file name="armeabi/libmp3lame.so" path="D:\Terminus\ZlwAudioRecorder\recorderlib\libs\armeabi\libmp3lame.so"/><file name="armeabi-v7a/libmp3lame.so" path="D:\Terminus\ZlwAudioRecorder\recorderlib\libs\armeabi-v7a\libmp3lame.so"/><file name="mips/libmp3lame.so" path="D:\Terminus\ZlwAudioRecorder\recorderlib\libs\mips\libmp3lame.so"/><file name="mips64/libmp3lame.so" path="D:\Terminus\ZlwAudioRecorder\recorderlib\libs\mips64\libmp3lame.so"/><file name="x86/libmp3lame.so" path="D:\Terminus\ZlwAudioRecorder\recorderlib\libs\x86\libmp3lame.so"/><file name="x86_64/libmp3lame.so" path="D:\Terminus\ZlwAudioRecorder\recorderlib\libs\x86_64\libmp3lame.so"/></source></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Terminus\ZlwAudioRecorder\recorderlib\src\debug\jniLibs"/></dataSet></merger>
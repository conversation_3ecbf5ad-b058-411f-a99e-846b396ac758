package com.zlw.audio_recorder.speech;

import android.os.Handler;
import android.os.Looper;

import com.zlw.loggerlib.Logger;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.json.JSONArray;
import org.json.JSONObject;

import java.net.URI;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;

/**
 * 语音识别WebSocket客户端
 */
public class SpeechRecognitionClient {
    private static final String TAG = SpeechRecognitionClient.class.getSimpleName();
    private static final String WEBSOCKET_URL = "wss://tai.tslsmart.com:10096/";
    private static final int CHUNK_SIZE = 960; // 音频块大小
    
    private WebSocketClient webSocketClient;
    private Handler mainHandler;
    private SpeechRecognitionListener listener;
    private List<Short> audioBuffer;
    private boolean isConnected = false;
    private StringBuilder recognitionTextBuffer; // 用于拼接识别文本
    
    public interface SpeechRecognitionListener {
        void onConnected();
        void onDisconnected();
        void onRecognitionResult(String text, boolean isFinal);
        void onError(String error);
    }
    
    public SpeechRecognitionClient(SpeechRecognitionListener listener) {
        this.listener = listener;
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.audioBuffer = new ArrayList<>();
        this.recognitionTextBuffer = new StringBuilder();
    }
    
    /**
     * 连接到语音识别服务器
     */
    public void connect() {
        try {
            URI serverUri = URI.create(WEBSOCKET_URL);
            
            webSocketClient = new WebSocketClient(serverUri) {
                @Override
                public void onOpen(ServerHandshake handshake) {
                    Logger.i(TAG, "WebSocket连接已建立");
                    isConnected = true;

                    // 重置识别文本缓冲区
                    recognitionTextBuffer.setLength(0);

                    // 发送初始化配置
                    sendInitConfig();

                    mainHandler.post(() -> {
                        if (listener != null) {
                            listener.onConnected();
                        }
                    });
                }
                
                @Override
                public void onMessage(String message) {
                    Logger.i(TAG, "收到WebSocket消息: %s", message);
                    handleRecognitionResult(message);
                }
                
                @Override
                public void onClose(int code, String reason, boolean remote) {
                    Logger.i(TAG, "WebSocket连接已关闭: code=%d, reason=%s", code, reason);
                    isConnected = false;
                    
                    mainHandler.post(() -> {
                        if (listener != null) {
                            listener.onDisconnected();
                        }
                    });
                }
                
                @Override
                public void onError(Exception ex) {
                    Logger.e(ex, TAG, "WebSocket连接错误: %s", ex.getMessage());
                    isConnected = false;
                    
                    mainHandler.post(() -> {
                        if (listener != null) {
                            listener.onError("WebSocket连接错误: " + ex.getMessage());
                        }
                    });
                }
            };
            
            Logger.i(TAG, "开始连接到语音识别服务器: %s", WEBSOCKET_URL);
            webSocketClient.connect();
            
        } catch (Exception e) {
            Logger.e(e, TAG, "创建WebSocket连接失败: %s", e.getMessage());
            if (listener != null) {
                listener.onError("创建WebSocket连接失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 发送初始化配置
     */
    private void sendInitConfig() {
        try {
            JSONObject config = new JSONObject();
            
            // 设置chunk_size数组 [5, 10, 5]
            JSONArray chunkSizeArray = new JSONArray();
            chunkSizeArray.put(5);
            chunkSizeArray.put(10);
            chunkSizeArray.put(5);
            
            config.put("chunk_size", chunkSizeArray);
            config.put("wav_name", "Android");
            config.put("is_speaking", true);
            config.put("chunk_interval", 10);
            config.put("itn", false);
            config.put("mode", "offline");
            
            String configString = config.toString();
            Logger.i(TAG, "发送初始化配置: %s", configString);
            
            webSocketClient.send(configString);
            
        } catch (Exception e) {
            Logger.e(e, TAG, "发送初始化配置失败: %s", e.getMessage());
        }
    }
    
    /**
     * 发送音频数据
     */
    public void sendAudioData(short[] audioData) {
        if (!isConnected || webSocketClient == null) {
            Logger.w(TAG, "WebSocket未连接，跳过音频数据发送");
            return;
        }

        if (audioData == null || audioData.length == 0) {
            Logger.w(TAG, "音频数据为空，跳过发送");
            return;
        }

        try {
            Logger.d(TAG, "接收到音频数据，长度: %d", audioData.length);

            // 将音频数据添加到缓冲区
            for (short sample : audioData) {
                audioBuffer.add(sample);
            }

            Logger.d(TAG, "当前音频缓冲区大小: %d, 需要: %d", audioBuffer.size(), CHUNK_SIZE);

            // 分块发送音频数据
            int sentChunks = 0;
            while (audioBuffer.size() >= CHUNK_SIZE) {
                // 提取一个chunk的数据
                short[] chunk = new short[CHUNK_SIZE];
                for (int i = 0; i < CHUNK_SIZE; i++) {
                    chunk[i] = audioBuffer.remove(0);
                }

                // 转换为ByteBuffer并发送
                ByteBuffer buffer = ByteBuffer.allocate(chunk.length * 2);
                for (short sample : chunk) {
                    buffer.putShort(sample);
                }

                webSocketClient.send(buffer.array());
                sentChunks++;
                Logger.i(TAG, "发送音频数据块 #%d，大小: %d bytes", sentChunks, buffer.array().length);
            }

            if (sentChunks > 0) {
                Logger.i(TAG, "本次共发送 %d 个音频数据块", sentChunks);
            }

        } catch (Exception e) {
            Logger.e(e, TAG, "发送音频数据失败: %s", e.getMessage());
        }
    }
    
    /**
     * 处理识别结果
     */
    private void handleRecognitionResult(String message) {
        try {
            JSONObject result = new JSONObject(message);
            Logger.d(TAG, "收到识别结果JSON: %s", message);

            // 解析is_final字段
            boolean isFinal = result.optBoolean("is_final", false);

            // 解析text字段
            String text = result.optString("text", "");

            if (isFinal) {
                // 识别流程结束
                Logger.i(TAG, "识别流程结束，最终文本: %s", recognitionTextBuffer.toString());

                final String finalText = recognitionTextBuffer.toString().trim();

                // 重置缓冲区
                recognitionTextBuffer.setLength(0);

                // 回调最终结果
                mainHandler.post(() -> {
                    if (listener != null && !finalText.isEmpty()) {
                        listener.onRecognitionResult(finalText, true);
                    }
                });

            } else {
                // 识别过程中的临时结果
                if (!text.isEmpty()) {
                    // 将新的文本追加到缓冲区
                    if (recognitionTextBuffer.length() > 0) {
                        recognitionTextBuffer.append(" ");
                    }
                    recognitionTextBuffer.append(text);

                    Logger.d(TAG, "追加识别文本: %s, 当前缓冲区: %s", text, recognitionTextBuffer.toString());

                    final String currentText = recognitionTextBuffer.toString().trim();

                    // 回调临时结果
                    mainHandler.post(() -> {
                        if (listener != null && !currentText.isEmpty()) {
                            listener.onRecognitionResult(currentText, false);
                        }
                    });
                }
            }

        } catch (Exception e) {
            Logger.e(e, TAG, "解析识别结果失败: %s", e.getMessage());
        }
    }
    
    /**
     * 断开连接
     */
    public void disconnect() {
        try {
            if (webSocketClient != null) {
                Logger.i(TAG, "断开WebSocket连接");
                webSocketClient.close();
                webSocketClient = null;
            }
            isConnected = false;
            audioBuffer.clear();

            // 重置识别文本缓冲区
            recognitionTextBuffer.setLength(0);

        } catch (Exception e) {
            Logger.e(e, TAG, "断开WebSocket连接失败: %s", e.getMessage());
        }
    }
    
    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return isConnected && webSocketClient != null && webSocketClient.isOpen();
    }
}

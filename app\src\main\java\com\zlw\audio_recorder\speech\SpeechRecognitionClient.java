package com.zlw.audio_recorder.speech;

import android.os.Handler;
import android.os.Looper;

import com.zlw.loggerlib.Logger;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.nio.ByteBuffer;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.TimeUnit;
import android.util.Base64;

import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import okhttp3.WebSocket;
import okhttp3.WebSocketListener;
import okio.ByteString;

/**
 * 语音识别WebSocket客户端
 */
public class SpeechRecognitionClient {
    private static final String TAG = SpeechRecognitionClient.class.getSimpleName();
    private static final String WEBSOCKET_URL = "wss://tai.tslsmart.com:10096/";
    private static final int CHUNK_SIZE = 960; // 音频块大小

    private OkHttpClient httpClient;
    private WebSocket webSocket;
    private Handler mainHandler;
    private SpeechRecognitionListener listener;
    private boolean isConnected = false;
    private StringBuilder recognitionTextBuffer; // 用于拼接识别文本
    private boolean isProcessingFile = false; // 是否正在处理文件
    
    public interface SpeechRecognitionListener {
        void onConnected();
        void onDisconnected();
        void onRecognitionResult(String text, boolean isFinal);
        void onError(String error);
    }
    
    public SpeechRecognitionClient(SpeechRecognitionListener listener) {
        this.listener = listener;
        this.mainHandler = new Handler(Looper.getMainLooper());
        this.recognitionTextBuffer = new StringBuilder();

        // 创建OkHttp客户端
        this.httpClient = new OkHttpClient.Builder()
                .connectTimeout(10, TimeUnit.SECONDS)
                .readTimeout(60, TimeUnit.SECONDS) // 增加读取超时，因为文件处理可能需要更长时间
                .writeTimeout(60, TimeUnit.SECONDS)
                .build();
    }
    
    /**
     * 连接到语音识别服务器
     */
    public void connect() {
        try {
            Request request = new Request.Builder()
                    .url(WEBSOCKET_URL)
                    .build();

            WebSocketListener webSocketListener = new WebSocketListener() {
                @Override
                public void onOpen(WebSocket webSocket, Response response) {
                    Logger.i(TAG, "WebSocket连接已建立，等待录音完成后处理文件");
                    isConnected = true;

                    // 重置识别文本缓冲区
                    recognitionTextBuffer.setLength(0);

                    mainHandler.post(() -> {
                        if (listener != null) {
                            listener.onConnected();
                        }
                    });
                }

                @Override
                public void onMessage(WebSocket webSocket, String text) {
                    Logger.i(TAG, "收到WebSocket消息: %s", text);
                    handleRecognitionResult(text);
                }

                @Override
                public void onMessage(WebSocket webSocket, ByteString bytes) {
                    Logger.d(TAG, "收到二进制消息，长度: %d", bytes.size());
                }

                @Override
                public void onClosing(WebSocket webSocket, int code, String reason) {
                    Logger.i(TAG, "WebSocket正在关闭: code=%d, reason=%s", code, reason);
                }

                @Override
                public void onClosed(WebSocket webSocket, int code, String reason) {
                    Logger.i(TAG, "WebSocket连接已关闭: code=%d, reason=%s", code, reason);
                    isConnected = false;

                    mainHandler.post(() -> {
                        if (listener != null) {
                            listener.onDisconnected();
                        }
                    });
                }

                @Override
                public void onFailure(WebSocket webSocket, Throwable t, Response response) {
                    Logger.e(t, TAG, "WebSocket连接失败: %s", t.getMessage());
                    isConnected = false;

                    mainHandler.post(() -> {
                        if (listener != null) {
                            listener.onError("WebSocket连接失败: " + t.getMessage());
                        }
                    });
                }
            };

            Logger.i(TAG, "开始连接到语音识别服务器: %s", WEBSOCKET_URL);
            webSocket = httpClient.newWebSocket(request, webSocketListener);

        } catch (Exception e) {
            Logger.e(e, TAG, "创建WebSocket连接失败: %s", e.getMessage());
            if (listener != null) {
                listener.onError("创建WebSocket连接失败: " + e.getMessage());
            }
        }
    }
    
    /**
     * 发送开始识别配置
     */
    private void sendStartConfig() {
        try {
            JSONObject config = new JSONObject();

            // 设置chunk_size数组 [5, 10, 5]
            JSONArray chunkSizeArray = new JSONArray();
            chunkSizeArray.put(5);
            chunkSizeArray.put(10);
            chunkSizeArray.put(5);

            config.put("chunk_size", chunkSizeArray);
            config.put("wav_name", "h5");
            config.put("is_speaking", true);
            config.put("chunk_interval", 10);
            config.put("itn", false);
            config.put("mode", "offline");
            config.put("wav_format", "PCM");
            config.put("audio_fs", 16000);
            config.put("hotwords", "{\"阿里巴巴\":20,\"hello world\":40}");

            String configString = config.toString();
            Logger.i(TAG, "发送开始识别配置: %s", configString);

            if (webSocket != null) {
                webSocket.send(configString);
            }

        } catch (Exception e) {
            Logger.e(e, TAG, "发送开始识别配置失败: %s", e.getMessage());
        }
    }

    /**
     * 发送结束识别配置
     */
    private void sendEndConfig() {
        try {
            JSONObject config = new JSONObject();

            // 设置chunk_size数组 [5, 10, 5]
            JSONArray chunkSizeArray = new JSONArray();
            chunkSizeArray.put(5);
            chunkSizeArray.put(10);
            chunkSizeArray.put(5);

            config.put("chunk_size", chunkSizeArray);
            config.put("wav_name", "h5");
            config.put("is_speaking", false);
            config.put("chunk_interval", 10);
            config.put("mode", "offline");

            String configString = config.toString();
            Logger.i(TAG, "发送结束识别配置: %s", configString);

            if (webSocket != null) {
                webSocket.send(configString);
            }

        } catch (Exception e) {
            Logger.e(e, TAG, "发送结束识别配置失败: %s", e.getMessage());
        }
    }
    
    /**
     * 处理录音文件进行语音识别
     */
    public void processAudioFile(File audioFile) {
        if (!isConnected || webSocket == null) {
            Logger.w(TAG, "WebSocket未连接，无法处理音频文件");
            if (listener != null) {
                mainHandler.post(() -> listener.onError("WebSocket未连接"));
            }
            return;
        }

        if (audioFile == null || !audioFile.exists()) {
            Logger.w(TAG, "音频文件不存在: %s", audioFile != null ? audioFile.getPath() : "null");
            if (listener != null) {
                mainHandler.post(() -> listener.onError("音频文件不存在"));
            }
            return;
        }

        if (isProcessingFile) {
            Logger.w(TAG, "正在处理其他文件，跳过当前请求");
            return;
        }

        isProcessingFile = true;
        Logger.i(TAG, "开始处理音频文件: %s, 大小: %d bytes", audioFile.getPath(), audioFile.length());

        // 在后台线程处理文件
        new Thread(() -> {
            try {
                // 1. 发送开始识别配置
                sendStartConfig();

                // 2. 读取并发送音频文件数据
                sendAudioFileData(audioFile);

                // 3. 发送结束识别配置
                sendEndConfig();

                Logger.i(TAG, "音频文件处理完成: %s", audioFile.getPath());

            } catch (Exception e) {
                Logger.e(e, TAG, "处理音频文件失败: %s", e.getMessage());
                if (listener != null) {
                    mainHandler.post(() -> listener.onError("处理音频文件失败: " + e.getMessage()));
                }
            } finally {
                isProcessingFile = false;
            }
        }).start();
    }

    /**
     * 读取音频文件并分块发送
     */
    private void sendAudioFileData(File audioFile) throws IOException {
        FileInputStream fis = null;
        try {
            fis = new FileInputStream(audioFile);
            byte[] buffer = new byte[CHUNK_SIZE * 2]; // 每个chunk 960个short，即1920字节
            int bytesRead;
            int chunkCount = 0;

            Logger.i(TAG, "开始发送音频文件数据，chunk大小: %d bytes", buffer.length);

            while ((bytesRead = fis.read(buffer)) != -1) {
                // 如果读取的字节数不足一个完整chunk，只发送实际读取的部分
                byte[] chunkData = new byte[bytesRead];
                System.arraycopy(buffer, 0, chunkData, 0, bytesRead);

                // 转换为Base64
                String base64Data = Base64.encodeToString(chunkData, Base64.NO_WRAP);

                // 发送Base64数据
                if (webSocket != null) {
                    webSocket.send(base64Data);
                    chunkCount++;
                    Logger.d(TAG, "发送音频chunk #%d，原始大小: %d bytes, Base64长度: %d",
                            chunkCount, bytesRead, base64Data.length());
                }

                // 添加小延迟，避免发送过快
                Thread.sleep(10);
            }

            Logger.i(TAG, "音频文件数据发送完成，共发送 %d 个chunks", chunkCount);

        } finally {
            if (fis != null) {
                try {
                    fis.close();
                } catch (IOException e) {
                    Logger.e(e, TAG, "关闭文件流失败");
                }
            }
        }
    }
    
    /**
     * 处理识别结果
     */
    private void handleRecognitionResult(String message) {
        try {
            JSONObject result = new JSONObject(message);
            Logger.d(TAG, "收到识别结果JSON: %s", message);

            // 解析is_final字段
            boolean isFinal = result.optBoolean("is_final", false);

            // 解析text字段
            String text = result.optString("text", "");

            if (isFinal) {
                // 识别流程结束
                Logger.i(TAG, "识别流程结束，最终文本: %s", recognitionTextBuffer.toString());

                final String finalText = recognitionTextBuffer.toString().trim();

                // 重置缓冲区
                recognitionTextBuffer.setLength(0);

                // 回调最终结果
                mainHandler.post(() -> {
                    if (listener != null && !finalText.isEmpty()) {
                        listener.onRecognitionResult(finalText, true);
                    }
                });

            } else {
                // 识别过程中的临时结果
                if (!text.isEmpty()) {
                    // 将新的文本追加到缓冲区
                    if (recognitionTextBuffer.length() > 0) {
                        recognitionTextBuffer.append(" ");
                    }
                    recognitionTextBuffer.append(text);

                    Logger.d(TAG, "追加识别文本: %s, 当前缓冲区: %s", text, recognitionTextBuffer.toString());

                    final String currentText = recognitionTextBuffer.toString().trim();

                    // 回调临时结果
                    mainHandler.post(() -> {
                        if (listener != null && !currentText.isEmpty()) {
                            listener.onRecognitionResult(currentText, false);
                        }
                    });
                }
            }

        } catch (Exception e) {
            Logger.e(e, TAG, "解析识别结果失败: %s", e.getMessage());
        }
    }
    
    /**
     * 断开连接
     */
    public void disconnect() {
        try {
            if (webSocket != null) {
                Logger.i(TAG, "断开WebSocket连接");
                webSocket.close(1000, "正常关闭");
                webSocket = null;
            }
            isConnected = false;
            isProcessingFile = false;

            // 重置识别文本缓冲区
            recognitionTextBuffer.setLength(0);

        } catch (Exception e) {
            Logger.e(e, TAG, "断开WebSocket连接失败: %s", e.getMessage());
        }
    }
    
    /**
     * 检查连接状态
     */
    public boolean isConnected() {
        return isConnected && webSocket != null;
    }
}

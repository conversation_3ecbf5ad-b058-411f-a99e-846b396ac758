<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Terminus\ZlwAudioRecorder\app\src\main\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="main" generated-set="main$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Terminus\ZlwAudioRecorder\app\src\main\res"><file name="button_danger" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable\button_danger.xml" qualifiers="" type="drawable"/><file name="button_primary" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable\button_primary.xml" qualifiers="" type="drawable"/><file name="button_secondary" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable\button_secondary.xml" qualifiers="" type="drawable"/><file name="card_background" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable\card_background.xml" qualifiers="" type="drawable"/><file name="gradient_background" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable\gradient_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_background" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable\ic_launcher_background.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground_new" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable\ic_launcher_foreground_new.xml" qualifiers="" type="drawable"/><file name="ic_microphone" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable\ic_microphone.xml" qualifiers="" type="drawable"/><file name="ic_pause" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable\ic_pause.xml" qualifiers="" type="drawable"/><file name="ic_stop" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable\ic_stop.xml" qualifiers="" type="drawable"/><file name="record_button" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable\record_button.xml" qualifiers="" type="drawable"/><file name="splash_background" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable\splash_background.xml" qualifiers="" type="drawable"/><file name="stop_button" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable\stop_button.xml" qualifiers="" type="drawable"/><file name="ic_launcher_foreground" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable-v24\ic_launcher_foreground.xml" qualifiers="v24" type="drawable"/><file name="activity_hz" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\layout\activity_hz.xml" qualifiers="" type="layout"/><file name="activity_main" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\layout\activity_main.xml" qualifiers="" type="layout"/><file name="tab_audio_format" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\layout\tab_audio_format.xml" qualifiers="" type="layout"/><file name="tab_audio_params" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\layout\tab_audio_params.xml" qualifiers="" type="layout"/><file name="tab_audio_source" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\layout\tab_audio_source.xml" qualifiers="" type="layout"/><file name="ic_launcher" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\mipmap-anydpi-v26\ic_launcher.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher_round" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\mipmap-anydpi-v26\ic_launcher_round.xml" qualifiers="anydpi-v26" type="mipmap"/><file name="ic_launcher" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\mipmap-hdpi\ic_launcher.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\mipmap-hdpi\ic_launcher_round.png" qualifiers="hdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\mipmap-mdpi\ic_launcher.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\mipmap-mdpi\ic_launcher_round.png" qualifiers="mdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\mipmap-xhdpi\ic_launcher.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\mipmap-xhdpi\ic_launcher_round.png" qualifiers="xhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\mipmap-xxhdpi\ic_launcher.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\mipmap-xxhdpi\ic_launcher_round.png" qualifiers="xxhdpi-v4" type="mipmap"/><file name="ic_launcher" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\mipmap-xxxhdpi\ic_launcher.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file name="ic_launcher_round" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\mipmap-xxxhdpi\ic_launcher_round.png" qualifiers="xxxhdpi-v4" type="mipmap"/><file path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\values\colors.xml" qualifiers=""><color name="colorPrimary">#6366F1</color><color name="colorPrimaryDark">#4F46E5</color><color name="colorAccent">#EC4899</color><color name="gradient_start">#667EEA</color><color name="gradient_end">#764BA2</color><color name="background_primary">#F8FAFC</color><color name="background_secondary">#FFFFFF</color><color name="background_card">#FFFFFF</color><color name="text_primary">#1E293B</color><color name="text_secondary">#64748B</color><color name="text_hint">#94A3B8</color><color name="recording_active">#EF4444</color><color name="recording_pause">#F59E0B</color><color name="recording_stop">#64748B</color><color name="divider">#E2E8F0</color><color name="card_shadow">#1A000000</color><color name="button_primary">#6366F1</color><color name="button_secondary">#F1F5F9</color><color name="button_danger">#EF4444</color><color name="success">#10B981</color><color name="warning">#F59E0B</color><color name="error">#EF4444</color><color name="info">#3B82F6</color></file><file path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\values\strings.xml" qualifiers=""><string name="app_name">Terminus展厅拾音助手</string><string name="app_name_en">Pro Audio Recorder</string></file><file path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\values\styles.xml" qualifiers=""><style name="Theme.ZlwAudioRecorder" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowBackground">@color/background_primary</item>
        <item name="android:statusBarColor">@color/colorPrimaryDark</item>
    </style><style name="CardStyle">
        <item name="android:background">@drawable/card_background</item>
        <item name="android:elevation">2dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:padding">16dp</item>
    </style><style name="PrimaryButtonStyle">
        <item name="android:background">@drawable/button_primary</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">16sp</item>
        <item name="android:elevation">4dp</item>
        <item name="android:minHeight">48dp</item>
    </style><style name="SecondaryButtonStyle">
        <item name="android:background">@drawable/button_secondary</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:elevation">2dp</item>
        <item name="android:minHeight">48dp</item>
    </style></file><file path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\values-night\colors.xml" qualifiers="night-v8"><color name="colorPrimary">#7C3AED</color><color name="colorPrimaryDark">#6D28D9</color><color name="colorAccent">#F472B6</color><color name="gradient_start">#7C3AED</color><color name="gradient_end">#A855F7</color><color name="background_primary">#0F172A</color><color name="background_secondary">#1E293B</color><color name="background_card">#1E293B</color><color name="text_primary">#F1F5F9</color><color name="text_secondary">#CBD5E1</color><color name="text_hint">#64748B</color><color name="recording_active">#EF4444</color><color name="recording_pause">#F59E0B</color><color name="recording_stop">#94A3B8</color><color name="divider">#334155</color><color name="card_shadow">#1A000000</color><color name="button_primary">#7C3AED</color><color name="button_secondary">#334155</color><color name="button_danger">#EF4444</color><color name="success">#10B981</color><color name="warning">#F59E0B</color><color name="error">#EF4444</color><color name="info">#3B82F6</color></file><file name="record_button_green" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable\record_button_green.xml" qualifiers="" type="drawable"/><file name="record_button_red" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable\record_button_red.xml" qualifiers="" type="drawable"/><file name="record_button_yellow" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable\record_button_yellow.xml" qualifiers="" type="drawable"/><file name="fragment_exhibition_room" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\layout\fragment_exhibition_room.xml" qualifiers="" type="layout"/><file name="button_green_selected" path="D:\Terminus\ZlwAudioRecorder\app\src\main\res\drawable\button_green_selected.xml" qualifiers="" type="drawable"/></source></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Terminus\ZlwAudioRecorder\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="debug" generated-set="debug$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Terminus\ZlwAudioRecorder\app\src\debug\res"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated$Generated" generated="true" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Terminus\ZlwAudioRecorder\app\build\generated\res\resValues\debug"/></dataSet><dataSet aapt-namespace="http://schemas.android.com/apk/res-auto" config="generated" generated-set="generated$Generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\Terminus\ZlwAudioRecorder\app\build\generated\res\resValues\debug"/></dataSet><mergedItems/></merger>
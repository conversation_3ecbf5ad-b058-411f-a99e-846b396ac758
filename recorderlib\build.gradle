apply plugin: 'com.android.library'
//apply plugin: 'com.github.dcendents.android-maven'
group='com.github.zhaolewei'

android {
    namespace = "com.zlw.main.recorderlib"
    compileSdkVersion 33
    defaultConfig {
        minSdkVersion 16
        versionCode 1
        versionName "1.0"

        testInstrumentationRunner "android.support.test.runner.AndroidJUnitRunner"
    }

    buildTypes {
        release {
            minifyEnabled false
            proguardFiles getDefaultProguardFile('proguard-android.txt'), 'proguard-rules.pro'
        }
    }
    sourceSets.main {
        jni.srcDirs = []//disable automatic ndk-build call
        jniLibs.srcDirs = ['libs']
    }
}

dependencies {
    implementation fileTree(dir: 'libs', include: ['*.jar'])
}

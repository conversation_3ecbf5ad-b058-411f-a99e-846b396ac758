package com.zlw.audio_recorder.network;

import com.zlw.audio_recorder.model.ExhibitionRoom;
import com.zlw.loggerlib.Logger;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * API客户端
 */
public class ApiClient {
    private static final String TAG = ApiClient.class.getSimpleName();
    private static final String ROOMS_URL = "https://parkexpo.tslsmart.com:21443/tai/v1/exhibition/rooms";
    private static final String SWITCH_ROOM_URL = "https://parkexpo.tslsmart.com:21443/tai/v1/exhibition/room";
    private static final String GET_CURRENT_ROOM_URL = "https://parkexpo.tslsmart.com:21443/tai/v1/exhibition/room";
    private static final String INTERRUPT_URL = "https://parkexpo.tslsmart.com:21443/tai/v1/exhibition/interrupt";
    private static final String VOICE_START_URL = "https://parkexpo.tslsmart.com:21443/tai/v1/exhibition/voice/start";
    private static final String VOICE_END_URL = "https://parkexpo.tslsmart.com:21443/tai/v1/exhibition/voice/end";
    private static final ExecutorService executor = Executors.newSingleThreadExecutor();

    public interface ApiCallback {
        void onSuccess(List<ExhibitionRoom> rooms);
        void onError(String error);
    }

    public interface SwitchRoomCallback {
        void onSuccess(String message);
        void onError(String error);
    }

    public interface InterruptCallback {
        void onSuccess(String message);
        void onError(String error);
    }

    public interface GetCurrentRoomCallback {
        void onSuccess(String currentRoom);
        void onError(String error);
    }

    public interface VoiceNotificationCallback {
        void onSuccess(String message);
        void onError(String error);
    }

    /**
     * 获取展厅列表
     */
    public static void getExhibitionRooms(String sessionId, ApiCallback callback) {
        executor.execute(() -> {
            try {
                String urlString = ROOMS_URL + "?sessionId=" + sessionId;
                Logger.i(TAG, "请求展厅列表: %s", urlString);
                
                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);
                connection.setRequestProperty("Accept", "application/json");
                
                int responseCode = connection.getResponseCode();
                Logger.i(TAG, "响应码: %d", responseCode);
                
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;
                    
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();
                    
                    String responseBody = response.toString();
                    Logger.i(TAG, "响应数据: %s", responseBody);
                    
                    List<ExhibitionRoom> rooms = parseResponse(responseBody);
                    callback.onSuccess(rooms);
                    
                } else {
                    String error = "HTTP错误: " + responseCode;
                    Logger.e(TAG, error);
                    callback.onError(error);
                }
                
                connection.disconnect();
                
            } catch (Exception e) {
                String error = "网络请求失败: " + e.getMessage();
                Logger.e(e, TAG, error);
                callback.onError(error);
            }
        });
    }

    /**
     * 解析响应数据
     */
    private static List<ExhibitionRoom> parseResponse(String responseBody) {
        List<ExhibitionRoom> rooms = new ArrayList<>();
        
        try {
            JSONArray jsonArray = new JSONArray(responseBody);
            
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                
                ExhibitionRoom room = new ExhibitionRoom();
                room.setSessionId(jsonObject.optString("sessionId"));
                room.setRoom(jsonObject.optString("room"));
                room.setProduct(jsonObject.optString("product"));
                
                rooms.add(room);
                Logger.i(TAG, "解析展厅: %s", room.toString());
            }
            
        } catch (Exception e) {
            Logger.e(e, TAG, "解析响应数据失败: %s", e.getMessage());
        }
        
        return rooms;
    }

    /**
     * 切换展厅
     */
    public static void switchRoom(String sessionId, String room, SwitchRoomCallback callback) {
        executor.execute(() -> {
            try {
                Logger.i(TAG, "切换展厅: sessionId=%s, room=%s", sessionId, room);

                URL url = new URL(SWITCH_ROOM_URL);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("POST");
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setRequestProperty("Accept", "application/json");
                connection.setDoOutput(true);

                // 构建请求体
                JSONObject requestBody = new JSONObject();
                requestBody.put("sessionId", sessionId);
                requestBody.put("room", room);

                String requestBodyString = requestBody.toString();
                Logger.i(TAG, "请求体: %s", requestBodyString);

                // 发送请求体
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = requestBodyString.getBytes(StandardCharsets.UTF_8);
                    os.write(input, 0, input.length);
                }

                int responseCode = connection.getResponseCode();
                Logger.i(TAG, "切换展厅响应码: %d", responseCode);

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;

                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();

                    String responseBody = response.toString();
                    Logger.i(TAG, "切换展厅响应数据: %s", responseBody);

                    // 解析响应
                    JSONObject jsonResponse = new JSONObject(responseBody);
                    int code = jsonResponse.optInt("code", -1);
                    boolean success = jsonResponse.optBoolean("success", false);
                    String message = jsonResponse.optString("message", "");
                    String data = jsonResponse.optString("data", "");

                    if (code == 0 && success) {
                        callback.onSuccess(data);
                        Logger.i(TAG, "切换展厅成功: %s", data);
                    } else {
                        String error = "切换失败: code=" + code + ", message=" + message;
                        callback.onError(error);
                        Logger.e(TAG, error);
                    }

                } else {
                    String error = "HTTP错误: " + responseCode;
                    Logger.e(TAG, error);
                    callback.onError(error);
                }

                connection.disconnect();

            } catch (Exception e) {
                String error = "切换展厅失败: " + e.getMessage();
                Logger.e(e, TAG, error);
                callback.onError(error);
            }
        });
    }

    /**
     * 打断操作
     */
    public static void interrupt(String sessionId, InterruptCallback callback) {
        executor.execute(() -> {
            try {
                Logger.i(TAG, "调用打断接口: sessionId=%s", sessionId);

                URL url = new URL(INTERRUPT_URL);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("POST");
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setRequestProperty("Accept", "application/json");
                connection.setDoOutput(true);

                // 构建请求体
                JSONObject requestBody = new JSONObject();
                requestBody.put("sessionId", sessionId);

                String requestBodyString = requestBody.toString();
                Logger.i(TAG, "打断请求体: %s", requestBodyString);

                // 发送请求体
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = requestBodyString.getBytes(StandardCharsets.UTF_8);
                    os.write(input, 0, input.length);
                }

                int responseCode = connection.getResponseCode();
                Logger.i(TAG, "打断接口响应码: %d", responseCode);

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;

                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();

                    String responseBody = response.toString();
                    Logger.i(TAG, "打断接口响应数据: %s", responseBody);

                    // 解析响应
                    JSONObject jsonResponse = new JSONObject(responseBody);
                    int code = jsonResponse.optInt("code", -1);
                    boolean success = jsonResponse.optBoolean("success", false);
                    String message = jsonResponse.optString("message", "");
                    String data = jsonResponse.optString("data", "");

                    if (code == 0 && success) {
                        callback.onSuccess(data);
                        Logger.i(TAG, "打断操作成功: %s", data);
                    } else {
                        String error = "打断失败: code=" + code + ", message=" + message;
                        callback.onError(error);
                        Logger.e(TAG, error);
                    }

                } else {
                    String error = "HTTP错误: " + responseCode;
                    Logger.e(TAG, error);
                    callback.onError(error);
                }

                connection.disconnect();

            } catch (Exception e) {
                String error = "打断操作失败: " + e.getMessage();
                Logger.e(e, TAG, error);
                callback.onError(error);
            }
        });
    }

    /**
     * 获取当前选择的展厅
     */
    public static void getCurrentRoom(String sessionId, GetCurrentRoomCallback callback) {
        executor.execute(() -> {
            try {
                String urlString = GET_CURRENT_ROOM_URL + "?sessionId=" + sessionId;
                Logger.i(TAG, "获取当前展厅: %s", urlString);

                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);
                connection.setRequestProperty("Accept", "application/json");

                int responseCode = connection.getResponseCode();
                Logger.i(TAG, "获取当前展厅响应码: %d", responseCode);

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;

                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();

                    String responseBody = response.toString();
                    Logger.i(TAG, "获取当前展厅响应数据: %s", responseBody);

                    // 解析响应
                    JSONObject jsonResponse = new JSONObject(responseBody);
                    String room = jsonResponse.optString("room", "");
                    String sessionIdFromResponse = jsonResponse.optString("sessionId", "");

                    if (!room.isEmpty()) {
                        callback.onSuccess(room);
                        Logger.i(TAG, "获取当前展厅成功: %s", room);
                    } else {
                        String error = "响应中没有room字段或room为空";
                        callback.onError(error);
                        Logger.e(TAG, error);
                    }

                } else {
                    String error = "HTTP错误: " + responseCode;
                    Logger.e(TAG, error);
                    callback.onError(error);
                }

                connection.disconnect();

            } catch (Exception e) {
                String error = "获取当前展厅失败: " + e.getMessage();
                Logger.e(e, TAG, error);
                callback.onError(error);
            }
        });
    }

    /**
     * 录音开始通知
     */
    public static void notifyVoiceStart(String sessionId, VoiceNotificationCallback callback) {
        executor.execute(() -> {
            try {
                Logger.i(TAG, "调用录音开始通知接口: sessionId=%s", sessionId);

                URL url = new URL(VOICE_START_URL);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("POST");
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setRequestProperty("Accept", "application/json");
                connection.setDoOutput(true);

                // 构建请求体
                JSONObject requestBody = new JSONObject();
                requestBody.put("sessionId", sessionId);

                String requestBodyString = requestBody.toString();
                Logger.i(TAG, "录音开始通知请求体: %s", requestBodyString);

                // 发送请求体
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = requestBodyString.getBytes(StandardCharsets.UTF_8);
                    os.write(input, 0, input.length);
                }

                int responseCode = connection.getResponseCode();
                Logger.i(TAG, "录音开始通知响应码: %d", responseCode);

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;

                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();

                    String responseBody = response.toString();
                    Logger.i(TAG, "录音开始通知响应数据: %s", responseBody);

                    // 解析响应
                    JSONObject jsonResponse = new JSONObject(responseBody);
                    int code = jsonResponse.optInt("code", -1);
                    boolean success = jsonResponse.optBoolean("success", false);
                    String message = jsonResponse.optString("message", "");
                    String data = jsonResponse.optString("data", "");

                    if (code == 0 && success) {
                        callback.onSuccess(data);
                        Logger.i(TAG, "录音开始通知成功: %s", data);
                    } else {
                        String error = "录音开始通知失败: code=" + code + ", message=" + message;
                        callback.onError(error);
                        Logger.e(TAG, error);
                    }

                } else {
                    String error = "HTTP错误: " + responseCode;
                    Logger.e(TAG, error);
                    callback.onError(error);
                }

                connection.disconnect();

            } catch (Exception e) {
                String error = "录音开始通知失败: " + e.getMessage();
                Logger.e(e, TAG, error);
                callback.onError(error);
            }
        });
    }

    /**
     * 录音结束通知
     */
    public static void notifyVoiceEnd(String sessionId, VoiceNotificationCallback callback) {
        executor.execute(() -> {
            try {
                Logger.i(TAG, "调用录音结束通知接口: sessionId=%s", sessionId);

                URL url = new URL(VOICE_END_URL);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("POST");
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);
                connection.setRequestProperty("Content-Type", "application/json");
                connection.setRequestProperty("Accept", "application/json");
                connection.setDoOutput(true);

                // 构建请求体
                JSONObject requestBody = new JSONObject();
                requestBody.put("sessionId", sessionId);

                String requestBodyString = requestBody.toString();
                Logger.i(TAG, "录音结束通知请求体: %s", requestBodyString);

                // 发送请求体
                try (OutputStream os = connection.getOutputStream()) {
                    byte[] input = requestBodyString.getBytes(StandardCharsets.UTF_8);
                    os.write(input, 0, input.length);
                }

                int responseCode = connection.getResponseCode();
                Logger.i(TAG, "录音结束通知响应码: %d", responseCode);

                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;

                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();

                    String responseBody = response.toString();
                    Logger.i(TAG, "录音结束通知响应数据: %s", responseBody);

                    // 解析响应
                    JSONObject jsonResponse = new JSONObject(responseBody);
                    int code = jsonResponse.optInt("code", -1);
                    boolean success = jsonResponse.optBoolean("success", false);
                    String message = jsonResponse.optString("message", "");
                    String data = jsonResponse.optString("data", "");

                    if (code == 0 && success) {
                        callback.onSuccess(data);
                        Logger.i(TAG, "录音结束通知成功: %s", data);
                    } else {
                        String error = "录音结束通知失败: code=" + code + ", message=" + message;
                        callback.onError(error);
                        Logger.e(TAG, error);
                    }

                } else {
                    String error = "HTTP错误: " + responseCode;
                    Logger.e(TAG, error);
                    callback.onError(error);
                }

                connection.disconnect();

            } catch (Exception e) {
                String error = "录音结束通知失败: " + e.getMessage();
                Logger.e(e, TAG, error);
                callback.onError(error);
            }
        });
    }
}

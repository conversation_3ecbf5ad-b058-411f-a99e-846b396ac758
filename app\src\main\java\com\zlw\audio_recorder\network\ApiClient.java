package com.zlw.audio_recorder.network;

import com.zlw.audio_recorder.model.ExhibitionRoom;
import com.zlw.loggerlib.Logger;

import org.json.JSONArray;
import org.json.JSONObject;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;

/**
 * API客户端
 */
public class ApiClient {
    private static final String TAG = ApiClient.class.getSimpleName();
    private static final String BASE_URL = "https://parkexpo.tslsmart.com:23443/tai/v1/exhibition/rooms";
    private static final ExecutorService executor = Executors.newSingleThreadExecutor();

    public interface ApiCallback {
        void onSuccess(List<ExhibitionRoom> rooms);
        void onError(String error);
    }

    /**
     * 获取展厅列表
     */
    public static void getExhibitionRooms(String sessionId, ApiCallback callback) {
        executor.execute(() -> {
            try {
                String urlString = BASE_URL + "?sessionId=" + sessionId;
                Logger.i(TAG, "请求展厅列表: %s", urlString);
                
                URL url = new URL(urlString);
                HttpURLConnection connection = (HttpURLConnection) url.openConnection();
                connection.setRequestMethod("GET");
                connection.setConnectTimeout(10000);
                connection.setReadTimeout(10000);
                connection.setRequestProperty("Accept", "application/json");
                
                int responseCode = connection.getResponseCode();
                Logger.i(TAG, "响应码: %d", responseCode);
                
                if (responseCode == HttpURLConnection.HTTP_OK) {
                    BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                    StringBuilder response = new StringBuilder();
                    String line;
                    
                    while ((line = reader.readLine()) != null) {
                        response.append(line);
                    }
                    reader.close();
                    
                    String responseBody = response.toString();
                    Logger.i(TAG, "响应数据: %s", responseBody);
                    
                    List<ExhibitionRoom> rooms = parseResponse(responseBody);
                    callback.onSuccess(rooms);
                    
                } else {
                    String error = "HTTP错误: " + responseCode;
                    Logger.e(TAG, error);
                    callback.onError(error);
                }
                
                connection.disconnect();
                
            } catch (Exception e) {
                String error = "网络请求失败: " + e.getMessage();
                Logger.e(e, TAG, error);
                callback.onError(error);
            }
        });
    }

    /**
     * 解析响应数据
     */
    private static List<ExhibitionRoom> parseResponse(String responseBody) {
        List<ExhibitionRoom> rooms = new ArrayList<>();
        
        try {
            JSONArray jsonArray = new JSONArray(responseBody);
            
            for (int i = 0; i < jsonArray.length(); i++) {
                JSONObject jsonObject = jsonArray.getJSONObject(i);
                
                ExhibitionRoom room = new ExhibitionRoom();
                room.setSessionId(jsonObject.optString("sessionId"));
                room.setRoom(jsonObject.optString("room"));
                room.setProduct(jsonObject.optString("product"));
                
                rooms.add(room);
                Logger.i(TAG, "解析展厅: %s", room.toString());
            }
            
        } catch (Exception e) {
            Logger.e(e, TAG, "解析响应数据失败: %s", e.getMessage());
        }
        
        return rooms;
    }
}

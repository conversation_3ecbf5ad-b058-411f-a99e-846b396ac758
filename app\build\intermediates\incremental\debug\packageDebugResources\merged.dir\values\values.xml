<?xml version="1.0" encoding="utf-8"?>
<resources>
    <color name="background_card">#FFFFFF</color>
    <color name="background_primary">#F8FAFC</color>
    <color name="background_secondary">#FFFFFF</color>
    <color name="button_danger">#EF4444</color>
    <color name="button_primary">#6366F1</color>
    <color name="button_secondary">#F1F5F9</color>
    <color name="card_shadow">#1A000000</color>
    <color name="colorAccent">#EC4899</color>
    <color name="colorPrimary">#6366F1</color>
    <color name="colorPrimaryDark">#4F46E5</color>
    <color name="divider">#E2E8F0</color>
    <color name="error">#EF4444</color>
    <color name="gradient_end">#764BA2</color>
    <color name="gradient_start">#667EEA</color>
    <color name="info">#3B82F6</color>
    <color name="recording_active">#EF4444</color>
    <color name="recording_pause">#F59E0B</color>
    <color name="recording_stop">#64748B</color>
    <color name="success">#10B981</color>
    <color name="text_hint">#94A3B8</color>
    <color name="text_primary">#1E293B</color>
    <color name="text_secondary">#64748B</color>
    <color name="warning">#F59E0B</color>
    <string name="app_name">专业录音工具</string>
    <string name="app_name_en">Pro Audio Recorder</string>
    <style name="CardStyle">
        <item name="android:background">@drawable/card_background</item>
        <item name="android:elevation">2dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:padding">16dp</item>
    </style>
    <style name="PrimaryButtonStyle">
        <item name="android:background">@drawable/button_primary</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">16sp</item>
        <item name="android:elevation">4dp</item>
        <item name="android:minHeight">48dp</item>
    </style>
    <style name="SecondaryButtonStyle">
        <item name="android:background">@drawable/button_secondary</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:elevation">2dp</item>
        <item name="android:minHeight">48dp</item>
    </style>
    <style name="Theme.ZlwAudioRecorder" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowBackground">@color/background_primary</item>
        <item name="android:statusBarColor">@color/colorPrimaryDark</item>
    </style>
</resources>
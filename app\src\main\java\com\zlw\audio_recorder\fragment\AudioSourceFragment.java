package com.zlw.audio_recorder.fragment;

import android.app.Activity;
import android.content.Intent;
import android.media.projection.MediaProjectionManager;
import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.zlw.audio_recorder.MainActivity;
import com.zlw.audio_recorder.R;
import com.zlw.main.recorderlib.RecordManager;
import com.zlw.main.recorderlib.recorder.RecordConfig;

import static android.content.Context.MEDIA_PROJECTION_SERVICE;

public class AudioSourceFragment extends Fragment {

    private RadioGroup tbSource;
    private RecordManager recordManager;
    private MediaProjectionManager mediaProjectionManager;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.tab_audio_source, container, false);
        
        tbSource = view.findViewById(R.id.tbSource);
        recordManager = RecordManager.getInstance();
        
        initEvent();
        
        return view;
    }

    private void initEvent() {
        tbSource.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                if (checkedId == R.id.rbMic) {
                    recordManager.setSource(RecordConfig.SOURCE_MIC);
                } else if (checkedId == R.id.rbSystem) {
                    recordManager.setSource(RecordConfig.SOURCE_SYSTEM);
                    if (getActivity() != null) {
                        mediaProjectionManager = (MediaProjectionManager) getActivity().getSystemService(MEDIA_PROJECTION_SERVICE);
                        Intent intent = mediaProjectionManager.createScreenCaptureIntent();
                        getActivity().startActivityForResult(intent, 2000);
                    }
                }
            }
        });
    }
}

<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

<LinearLayout
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="选择音频格式"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <RadioGroup
        android:id="@+id/rgAudioFormat"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RadioButton
            android:id="@+id/rbWav"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:checked="true"
            android:text="WAV - 无损音频格式"
            android:textColor="@color/text_secondary"
            android:buttonTint="@color/colorPrimary"
            android:layout_marginBottom="12dp" />

        <RadioButton
            android:id="@+id/rbMp3"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="MP3 - 压缩音频格式"
            android:textColor="@color/text_secondary"
            android:buttonTint="@color/colorPrimary"
            android:layout_marginBottom="12dp" />

        <RadioButton
            android:id="@+id/rbPcm"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="PCM - 原始音频数据"
            android:textColor="@color/text_secondary"
            android:buttonTint="@color/colorPrimary" />

    </RadioGroup>

</LinearLayout>

</ScrollView>

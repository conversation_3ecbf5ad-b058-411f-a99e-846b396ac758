package com.zlw.audio_recorder.adapter;

import androidx.annotation.NonNull;
import androidx.fragment.app.Fragment;
import androidx.fragment.app.FragmentActivity;
import androidx.viewpager2.adapter.FragmentStateAdapter;

import com.zlw.audio_recorder.fragment.AudioFormatFragment;
import com.zlw.audio_recorder.fragment.AudioParamsFragment;
import com.zlw.audio_recorder.fragment.AudioSourceFragment;

public class SettingsTabAdapter extends FragmentStateAdapter {

    public SettingsTabAdapter(@NonNull FragmentActivity fragmentActivity) {
        super(fragmentActivity);
    }

    @NonNull
    @Override
    public Fragment createFragment(int position) {
        switch (position) {
            case 0:
                return new AudioFormatFragment();
            case 1:
                return new AudioParamsFragment();
            case 2:
                return new AudioSourceFragment();
            default:
                return new AudioFormatFragment();
        }
    }

    @Override
    public int getItemCount() {
        return 3;
    }
}

<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    tools:context=".MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- 标题区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/gradient_background"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="24dp"
            android:layout_marginBottom="24dp"
            android:elevation="4dp">

            <ImageView
                android:layout_width="48dp"
                android:layout_height="48dp"
                android:src="@drawable/ic_microphone"
                android:layout_marginBottom="12dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="专业录音工具"
                android:textColor="#FFFFFF"
                android:textSize="24sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="高品质音频录制与处理"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:alpha="0.9" />

        </LinearLayout>

        <!-- 状态显示卡片 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/card_background"
            android:orientation="vertical"
            android:padding="20dp"
            android:layout_marginBottom="20dp"
            android:elevation="2dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="录音状态"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="12dp" />

            <TextView
                android:id="@+id/tvState"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="空闲中"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/tvSoundSize"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="声音大小：--- db"
                android:textColor="@color/text_secondary"
                android:textSize="14sp" />

        </LinearLayout>

        <!-- 音频格式选择卡片 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/card_background"
            android:orientation="vertical"
            android:padding="20dp"
            android:layout_marginBottom="20dp"
            android:elevation="2dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="音频格式"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <RadioGroup
                android:id="@+id/rgAudioFormat"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/rbPcm"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="PCM"
                    android:textColor="@color/text_secondary"
                    android:buttonTint="@color/colorPrimary" />

                <RadioButton
                    android:id="@+id/rbMp3"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="MP3"
                    android:textColor="@color/text_secondary"
                    android:buttonTint="@color/colorPrimary" />

                <RadioButton
                    android:id="@+id/rbWav"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:checked="true"
                    android:text="WAV"
                    android:textColor="@color/text_secondary"
                    android:buttonTint="@color/colorPrimary" />

            </RadioGroup>

        </LinearLayout>

        <!-- 音频参数设置卡片 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/card_background"
            android:orientation="vertical"
            android:padding="20dp"
            android:layout_marginBottom="20dp"
            android:elevation="2dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="音频参数"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <!-- 采样率 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="采样率"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <RadioGroup
                android:id="@+id/rgSimpleRate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginBottom="20dp">

                <RadioButton
                    android:id="@+id/rb8K"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="8kHz"
                    android:textColor="@color/text_secondary"
                    android:buttonTint="@color/colorPrimary" />

                <RadioButton
                    android:id="@+id/rb16K"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:checked="true"
                    android:text="16kHz"
                    android:textColor="@color/text_secondary"
                    android:buttonTint="@color/colorPrimary" />

                <RadioButton
                    android:id="@+id/rb44K"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="44.1kHz"
                    android:textColor="@color/text_secondary"
                    android:buttonTint="@color/colorPrimary" />
            </RadioGroup>

            <!-- 位宽 -->
            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="位宽"
                android:textColor="@color/text_secondary"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <RadioGroup
                android:id="@+id/tbEncoding"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal">

                <RadioButton
                    android:id="@+id/rb8Bit"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="8 Bit"
                    android:textColor="@color/text_secondary"
                    android:buttonTint="@color/colorPrimary" />

                <RadioButton
                    android:id="@+id/rb16Bit"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:checked="true"
                    android:text="16 Bit"
                    android:textColor="@color/text_secondary"
                    android:buttonTint="@color/colorPrimary" />
            </RadioGroup>

        </LinearLayout>

        <!-- 音源选择卡片 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/card_background"
            android:orientation="vertical"
            android:padding="20dp"
            android:layout_marginBottom="24dp"
            android:elevation="2dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="音源选择"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <RadioGroup
                android:id="@+id/tbSource"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <RadioButton
                    android:id="@+id/rbMic"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:text="麦克风录音"
                    android:textColor="@color/text_secondary"
                    android:buttonTint="@color/colorPrimary"
                    android:layout_marginBottom="8dp" />

                <RadioButton
                    android:id="@+id/rbSystem"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="系统内录 (Android 10+)"
                    android:textColor="@color/text_secondary"
                    android:buttonTint="@color/colorPrimary" />
            </RadioGroup>

        </LinearLayout>

        <!-- 录音控制区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:layout_marginBottom="24dp">

            <Button
                android:id="@+id/btRecord"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:background="@drawable/record_button"
                android:drawableTop="@drawable/ic_microphone"
                android:text=""
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:layout_marginEnd="20dp"
                android:elevation="6dp" />

            <Button
                android:id="@+id/btStop"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:background="@drawable/stop_button"
                android:drawableTop="@drawable/ic_stop"
                android:text=""
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:elevation="4dp" />

        </LinearLayout>

        <!-- 音频可视化卡片 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/card_background"
            android:orientation="vertical"
            android:padding="20dp"
            android:layout_marginBottom="20dp"
            android:elevation="2dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="音频可视化"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <com.zlw.audio_recorder.widget.AudioView
                android:id="@+id/audioView"
                android:layout_width="match_parent"
                android:layout_height="120dp"
                android:layout_marginBottom="16dp" />

            <!-- 可视化样式设置 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="12dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="上半部样式"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                    <Spinner
                        android:id="@+id/spUpStyle"
                        android:layout_width="120dp"
                        android:layout_height="wrap_content"
                        android:spinnerMode="dropdown" />
                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="下半部样式"
                        android:textColor="@color/text_secondary"
                        android:textSize="14sp" />

                    <Spinner
                        android:id="@+id/spDownStyle"
                        android:layout_width="120dp"
                        android:layout_height="wrap_content"
                        android:spinnerMode="dropdown" />
                </LinearLayout>
            </LinearLayout>

        </LinearLayout>

        <!-- 底部按钮 -->
        <Button
            android:id="@+id/jumpTestActivity"
            android:layout_width="match_parent"
            android:layout_height="48dp"
            android:background="@drawable/button_secondary"
            android:text="全屏显示"
            android:textColor="@color/text_primary"
            android:textSize="16sp"
            android:layout_marginBottom="20dp"
            android:elevation="2dp" />

    </LinearLayout>

</ScrollView>

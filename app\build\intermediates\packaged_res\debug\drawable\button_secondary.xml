<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item android:state_pressed="true">
        <shape android:shape="rectangle">
            <solid android:color="#E2E8F0" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="@color/divider" />
        </shape>
    </item>
    <item>
        <shape android:shape="rectangle">
            <solid android:color="@color/button_secondary" />
            <corners android:radius="12dp" />
            <stroke
                android:width="1dp"
                android:color="@color/divider" />
        </shape>
    </item>
</selector>

<?xml version="1.0" encoding="utf-8"?>

<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:gravity="center_horizontal"
    android:orientation="vertical"
    tools:context=".MainActivity">

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop" />

    <LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
        xmlns:tools="http://schemas.android.com/tools"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="#aa000000"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        tools:context=".MainActivity">


        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="horizontal">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:orientation="vertical">

                <Spinner
                    android:id="@+id/spUpStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:spinnerMode="dropdown" />

                <Spinner
                    android:id="@+id/spDownStyle"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:spinnerMode="dropdown" />
            </LinearLayout>

            <Button
                android:id="@+id/btRecord"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="录音"
                android:textSize="10sp" />

            <Button
                android:id="@+id/btStop"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="停止"
                android:textSize="10sp" />
        </LinearLayout>

        <RelativeLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginTop="60dp">

            <com.zlw.audio_recorder.widget.AudioView
                android:id="@+id/audioView"
                android:layout_width="match_parent"
                android:layout_height="160dp" />

        </RelativeLayout>

        <TextView
            android:id="@+id/tvState"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="状态：%s" />

    </LinearLayout>
</RelativeLayout>

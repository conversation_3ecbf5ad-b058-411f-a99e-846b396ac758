{"logs": [{"outputFile": "com.zlw.audio_recorder.app-mergeDebugResources-54:/values-be/values-be.xml", "map": [{"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\b550106afafe1b5d56eab8f95a181fdb\\transformed\\material-1.9.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,382,459,536,618,715,807,904,1036,1119,1186,1279,1356,1419,1535,1598,1667,1726,1797,1856,1910,2031,2092,2155,2209,2282,2404,2492,2575,2727,2813,2900,2991,3048,3099,3165,3237,3314,3398,3473,3550,3632,3708,3797,3879,3970,4066,4140,4221,4316,4370,4436,4523,4609,4671,4735,4798,4908,5015,5118,5227,5288,5343", "endLines": "7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69", "endColumns": "12,76,76,81,96,91,96,131,82,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,82,151,85,86,90,56,50,65,71,76,83,74,76,81,75,88,81,90,95,73,80,94,53,65,86,85,61,63,62,109,106,102,108,60,54,79", "endOffsets": "377,454,531,613,710,802,899,1031,1114,1181,1274,1351,1414,1530,1593,1662,1721,1792,1851,1905,2026,2087,2150,2204,2277,2399,2487,2570,2722,2808,2895,2986,3043,3094,3160,3232,3309,3393,3468,3545,3627,3703,3792,3874,3965,4061,4135,4216,4311,4365,4431,4518,4604,4666,4730,4793,4903,5010,5113,5222,5283,5338,5418"}, "to": {"startLines": "2,35,36,37,38,39,40,41,42,50,52,53,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,110", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,3162,3239,3316,3398,3495,3587,3684,3816,4517,4662,4755,5001,5064,5180,5243,5312,5371,5442,5501,5555,5676,5737,5800,5854,5927,6049,6137,6220,6372,6458,6545,6636,6693,6744,6810,6882,6959,7043,7118,7195,7277,7353,7442,7524,7615,7711,7785,7866,7961,8015,8081,8168,8254,8316,8380,8443,8553,8660,8763,8872,8933,9299", "endLines": "7,35,36,37,38,39,40,41,42,50,52,53,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,110", "endColumns": "12,76,76,81,96,91,96,131,82,66,92,76,62,115,62,68,58,70,58,53,120,60,62,53,72,121,87,82,151,85,86,90,56,50,65,71,76,83,74,76,81,75,88,81,90,95,73,80,94,53,65,86,85,61,63,62,109,106,102,108,60,54,79", "endOffsets": "427,3234,3311,3393,3490,3582,3679,3811,3894,4579,4750,4827,5059,5175,5238,5307,5366,5437,5496,5550,5671,5732,5795,5849,5922,6044,6132,6215,6367,6453,6540,6631,6688,6739,6805,6877,6954,7038,7113,7190,7272,7348,7437,7519,7610,7706,7780,7861,7956,8010,8076,8163,8249,8311,8375,8438,8548,8655,8758,8867,8928,8983,9374"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\7ba74c55d38703961fa899c39b0cb95d\\transformed\\core-1.9.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "100", "endOffsets": "151"}, "to": {"startLines": "115", "startColumns": "4", "startOffsets": "9703", "endColumns": "100", "endOffsets": "9799"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\d46bbf011d3a9c0836df2500e470bf3d\\transformed\\appcompat-1.5.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,225,328,444,530,635,754,834,911,1003,1097,1192,1286,1381,1475,1571,1666,1758,1850,1931,2037,2142,2240,2348,2454,2562,2735,2835", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "220,323,439,525,630,749,829,906,998,1092,1187,1281,1376,1470,1566,1661,1753,1845,1926,2032,2137,2235,2343,2449,2557,2730,2830,2912"}, "to": {"startLines": "8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,113", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "432,552,655,771,857,962,1081,1161,1238,1330,1424,1519,1613,1708,1802,1898,1993,2085,2177,2258,2364,2469,2567,2675,2781,2889,3062,9549", "endColumns": "119,102,115,85,104,118,79,76,91,93,94,93,94,93,95,94,91,91,80,105,104,97,107,105,107,172,99,81", "endOffsets": "547,650,766,852,957,1076,1156,1233,1325,1419,1514,1608,1703,1797,1893,1988,2080,2172,2253,2359,2464,2562,2670,2776,2884,3057,3157,9626"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\2f11cd048c7739820e5cda721fe9c715\\transformed\\jetified-material3-1.0.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4", "startColumns": "4,4,4", "startOffsets": "55,132,213", "endColumns": "76,80,77", "endOffsets": "127,208,286"}, "to": {"startLines": "45,48,51", "startColumns": "4,4,4", "startOffsets": "4076,4350,4584", "endColumns": "76,80,77", "endOffsets": "4148,4426,4657"}}, {"source": "D:\\AndroidStudio\\.gradle\\caches\\8.12\\transforms\\c7c7cbb5ee8217bc10c58177f979464a\\transformed\\jetified-ui-1.4.0\\res\\values-be\\values-be.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,198,282,376,479,565,645,734,822,904,975,1045,1128,1215,1287,1372,1442", "endColumns": "92,83,93,102,85,79,88,87,81,70,69,82,86,71,84,69,122", "endOffsets": "193,277,371,474,560,640,729,817,899,970,1040,1123,1210,1282,1367,1437,1560"}, "to": {"startLines": "43,44,46,47,49,54,55,106,107,108,109,111,112,114,116,117,118", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3899,3992,4153,4247,4431,4832,4912,8988,9076,9158,9229,9379,9462,9631,9804,9889,9959", "endColumns": "92,83,93,102,85,79,88,87,81,70,69,82,86,71,84,69,122", "endOffsets": "3987,4071,4242,4345,4512,4907,4996,9071,9153,9224,9294,9457,9544,9698,9884,9954,10077"}}]}]}
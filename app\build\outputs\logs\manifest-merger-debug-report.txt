-- Merging decision tree log ---
manifest
ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:2:1-41:12
INJECTED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:2:1-41:12
MERGED from [androidx.compose.material3:material3:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\2f11cd048c7739820e5cda721fe9c715\transformed\jetified-material3-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:2:1-52:12
MERGED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:17:1-61:12
MERGED from [androidx.appcompat:appcompat:1.1.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\cfb6fab0fe63b279fbdaae6be17ab184\transformed\appcompat-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.fragment:fragment:1.1.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\643bf6b8df8b01e9eec5f6254ff6df10\transformed\fragment-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.activity:activity:1.7.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\81290c9d9d4e25e56507b26115115af4\transformed\jetified-activity-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-ktx:1.7.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\18a56152153876fadd75e256bea7c2b3\transformed\jetified-activity-ktx-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.activity:activity-compose:1.7.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\432453ece6f0a50266aae316e8fc7e38\transformed\jetified-activity-compose-1.7.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation-core:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\5dbb12a330f96ade7c26d2b7ace30254\transformed\jetified-animation-core-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-icons-core:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\8e2a417ab5e3ef1788a9e3dd44f12e98\transformed\jetified-material-icons-core-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.material:material-ripple:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\f1ba2858edd15ef11706a5ea7945a613\transformed\jetified-material-ripple-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.animation:animation:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\cc84f0423a194e02b8f56530193a656e\transformed\jetified-animation-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.foundation:foundation-layout:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\eae75f066e2ca1ad56770242fec34183\transformed\jetified-foundation-layout-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.foundation:foundation:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\ac4cd5b8d926e1bc189e42781f971bc9\transformed\jetified-foundation-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-unit:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\71a3fe4c7eb6ff403e1f38b850b1812a\transformed\jetified-ui-unit-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-geometry:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\68ee56b063dab3fa3a73c0bd9b08b08a\transformed\jetified-ui-geometry-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-util:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\f40300e21ab8c96c48df45480cf990f3\transformed\jetified-ui-util-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-text:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\04fd93cc01590b7570b45e8c8ca1801b\transformed\jetified-ui-text-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-tooling-preview:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\6f46276e8793b12cd8a148055733a25e\transformed\jetified-ui-tooling-preview-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui-graphics:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\45dc80e24c09f4935f14c73f52035406\transformed\jetified-ui-graphics-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.ui:ui:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\c7c7cbb5ee8217bc10c58177f979464a\transformed\jetified-ui-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\639cfffdd3e9962f1bdf5d516e9ecbdc\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:17:1-23:12
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.loader:loader:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\c8ebaa811b9f24841dcc3872d67a98c5\transformed\loader-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\24629088d5ce7fc4202dcb8cb263e4e4\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\342d9974a43f383879650a00a56d9e2f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\a8db42fd17dc95f308887add3fd2816d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:17:1-35:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\af677c7be54292da0008d8483345bb02\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\c29cd734529d008ae2da02db61473f9d\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\ced8041d0ea224d8c1d7111b258b03f8\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\e1ce0c652e870898c214ee480cfa5893\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\3df553aee2a5f5aae57083cedb52c47e\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core-ktx:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\ae4c261d653bd49f5b92bfef76ab4d2d\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.autofill:autofill:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\932f57987d994be3862f97e80f511128\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4d55a293d403b9cf73bd36431b123f6\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\5bf77793adb19aca6e143d091bed880f\transformed\drawerlayout-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\235a8531292a6dd263ba912db4ff835b\transformed\viewpager-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\de9607de16116170e4ec920a3d531453\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\c2de4622f43fdcb84d47977c1d86ddcd\transformed\vectordrawable-1.1.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.customview:customview:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\8b93828b6f0a3e18bdbc32b949aad7b0\transformed\customview-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:17:1-30:12
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\d68cc036a2de5527e7a8088fc46ea908\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\263c5fa5ec182735fe9726c6a053e6e4\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime-saveable:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\220ed81d14c5e53d85ff494b89f0810f\transformed\jetified-runtime-saveable-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.compose.runtime:runtime:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\2fdbcf7e54ac151e4391bd2d88f3b41d\transformed\jetified-runtime-1.4.0\AndroidManifest.xml:17:1-22:12
MERGED from [com.github.zhaolewei:Logger:1.0.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\bfca0ae0b2fab14f7213612309dac8f6\transformed\jetified-Logger-1.0.2\AndroidManifest.xml:2:1-11:12
MERGED from [:recorderlib] D:\Terminus\ZlwAudioRecorder\recorderlib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:2:1-7:12
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:17:1-55:12
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\438d9746f04723b5e421bd604bd47109\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:17:1-27:12
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\bc975d9d0891b4d3eb064dcdf7727a99\transformed\core-runtime-2.2.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\40aa313f9bb2163d4fe7469629902cd8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\7f55c2808d12cbf97bc3331071c16400\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:17:1-33:12
MERGED from [androidx.tracing:tracing:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\fd3d8bb60b5e099f49c2f660c186f9bc\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:17:1-24:12
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\d4b50ca9aea5ab4fc9c1aa3252702ae2\transformed\interpolator-1.0.0\AndroidManifest.xml:17:1-22:12
MERGED from [androidx.annotation:annotation-experimental:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\35c434c7e161f07e04d97a3e66c54cad\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:17:1-22:12
	package
		INJECTED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml
	android:versionName
		INJECTED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml
	xmlns:tools
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:3:5-51
	android:versionCode
		INJECTED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml
	xmlns:android
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:2:11-69
uses-permission#android.permission.INTERNET
ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:5:5-67
	android:name
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:5:22-64
uses-permission#android.permission.RECORD_AUDIO
ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:6:5-71
	android:name
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:6:22-68
uses-permission#android.permission.WRITE_EXTERNAL_STORAGE
ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:7:5-81
	android:name
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:7:22-78
uses-permission#android.permission.READ_EXTERNAL_STORAGE
ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:8:5-80
	android:name
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:8:22-77
application
ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:11:5-39:19
INJECTED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:11:5-39:19
MERGED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:18:5-50:19
MERGED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:18:5-50:19
MERGED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:31:5-59:19
MERGED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:31:5-59:19
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\a8db42fd17dc95f308887add3fd2816d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\a8db42fd17dc95f308887add3fd2816d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:23:5-33:19
MERGED from [androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:28:5-89
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:23:5-53:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\438d9746f04723b5e421bd604bd47109\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\438d9746f04723b5e421bd604bd47109\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:24:5-25:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\7f55c2808d12cbf97bc3331071c16400\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\7f55c2808d12cbf97bc3331071c16400\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:25:5-31:19
	android:extractNativeLibs
		INJECTED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml
	android:appComponentFactory
		ADDED from [androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:28:18-86
	android:supportsRtl
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:17:9-35
	android:label
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:15:9-41
	android:roundIcon
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:16:9-54
	tools:targetApi
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:19:9-29
	android:icon
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:14:9-43
	android:allowBackup
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:13:9-35
	android:theme
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:18:9-54
	android:name
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:12:9-35
activity#com.zlw.audio_recorder.MainActivity
ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:20:9-29:20
	android:exported
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:22:13-36
	android:theme
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:23:13-58
	android:name
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:21:13-41
intent-filter#action:name:android.intent.action.MAIN+category:name:android.intent.category.LAUNCHER
ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:24:13-28:29
action#android.intent.action.MAIN
ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:25:17-69
	android:name
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:25:25-66
category#android.intent.category.LAUNCHER
ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:27:17-77
	android:name
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:27:27-74
activity#com.zlw.audio_recorder.TestHzActivity
ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:30:9-33:53
	android:screenOrientation
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:33:13-50
	android:label
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:32:13-33
	android:name
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:31:13-43
service#com.zlw.main.recorderlib.recorder.RecordService
ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:35:9-38:40
	android:enabled
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:38:13-37
	android:name
		ADDED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml:36:13-75
uses-sdk
INJECTED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml reason: use-sdk injection requested
INJECTED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml
INJECTED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml
MERGED from [androidx.compose.material3:material3:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\2f11cd048c7739820e5cda721fe9c715\transformed\jetified-material3-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material3:material3:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\2f11cd048c7739820e5cda721fe9c715\transformed\jetified-material3-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:7:5-44
MERGED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:7:5-44
MERGED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:20:5-22:41
MERGED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\cfb6fab0fe63b279fbdaae6be17ab184\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat:1.1.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\cfb6fab0fe63b279fbdaae6be17ab184\transformed\appcompat-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\643bf6b8df8b01e9eec5f6254ff6df10\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.fragment:fragment:1.1.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\643bf6b8df8b01e9eec5f6254ff6df10\transformed\fragment-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.activity:activity:1.7.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\81290c9d9d4e25e56507b26115115af4\transformed\jetified-activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity:1.7.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\81290c9d9d4e25e56507b26115115af4\transformed\jetified-activity-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\18a56152153876fadd75e256bea7c2b3\transformed\jetified-activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-ktx:1.7.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\18a56152153876fadd75e256bea7c2b3\transformed\jetified-activity-ktx-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.7.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\432453ece6f0a50266aae316e8fc7e38\transformed\jetified-activity-compose-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.activity:activity-compose:1.7.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\432453ece6f0a50266aae316e8fc7e38\transformed\jetified-activity-compose-1.7.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\5dbb12a330f96ade7c26d2b7ace30254\transformed\jetified-animation-core-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation-core:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\5dbb12a330f96ade7c26d2b7ace30254\transformed\jetified-animation-core-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-core:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\8e2a417ab5e3ef1788a9e3dd44f12e98\transformed\jetified-material-icons-core-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-icons-core:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\8e2a417ab5e3ef1788a9e3dd44f12e98\transformed\jetified-material-icons-core-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\f1ba2858edd15ef11706a5ea7945a613\transformed\jetified-material-ripple-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.material:material-ripple:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\f1ba2858edd15ef11706a5ea7945a613\transformed\jetified-material-ripple-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\cc84f0423a194e02b8f56530193a656e\transformed\jetified-animation-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.animation:animation:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\cc84f0423a194e02b8f56530193a656e\transformed\jetified-animation-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation-layout:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\eae75f066e2ca1ad56770242fec34183\transformed\jetified-foundation-layout-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation-layout:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\eae75f066e2ca1ad56770242fec34183\transformed\jetified-foundation-layout-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\ac4cd5b8d926e1bc189e42781f971bc9\transformed\jetified-foundation-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.foundation:foundation:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\ac4cd5b8d926e1bc189e42781f971bc9\transformed\jetified-foundation-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\71a3fe4c7eb6ff403e1f38b850b1812a\transformed\jetified-ui-unit-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-unit:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\71a3fe4c7eb6ff403e1f38b850b1812a\transformed\jetified-ui-unit-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-geometry:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\68ee56b063dab3fa3a73c0bd9b08b08a\transformed\jetified-ui-geometry-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-geometry:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\68ee56b063dab3fa3a73c0bd9b08b08a\transformed\jetified-ui-geometry-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\f40300e21ab8c96c48df45480cf990f3\transformed\jetified-ui-util-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-util:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\f40300e21ab8c96c48df45480cf990f3\transformed\jetified-ui-util-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\04fd93cc01590b7570b45e8c8ca1801b\transformed\jetified-ui-text-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-text:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\04fd93cc01590b7570b45e8c8ca1801b\transformed\jetified-ui-text-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\6f46276e8793b12cd8a148055733a25e\transformed\jetified-ui-tooling-preview-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-tooling-preview:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\6f46276e8793b12cd8a148055733a25e\transformed\jetified-ui-tooling-preview-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\45dc80e24c09f4935f14c73f52035406\transformed\jetified-ui-graphics-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui-graphics:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\45dc80e24c09f4935f14c73f52035406\transformed\jetified-ui-graphics-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\c7c7cbb5ee8217bc10c58177f979464a\transformed\jetified-ui-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.ui:ui:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\c7c7cbb5ee8217bc10c58177f979464a\transformed\jetified-ui-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\639cfffdd3e9962f1bdf5d516e9ecbdc\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.customview:customview-poolingcontainer:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\639cfffdd3e9962f1bdf5d516e9ecbdc\transformed\jetified-customview-poolingcontainer-1.0.0\AndroidManifest.xml:20:5-21:38
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\c8ebaa811b9f24841dcc3872d67a98c5\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.loader:loader:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\c8ebaa811b9f24841dcc3872d67a98c5\transformed\loader-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\24629088d5ce7fc4202dcb8cb263e4e4\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\24629088d5ce7fc4202dcb8cb263e4e4\transformed\lifecycle-livedata-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\342d9974a43f383879650a00a56d9e2f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-livedata-core:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\342d9974a43f383879650a00a56d9e2f\transformed\lifecycle-livedata-core-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\a8db42fd17dc95f308887add3fd2816d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\a8db42fd17dc95f308887add3fd2816d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:21:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\af677c7be54292da0008d8483345bb02\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-ktx:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\af677c7be54292da0008d8483345bb02\transformed\jetified-lifecycle-viewmodel-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\c29cd734529d008ae2da02db61473f9d\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\c29cd734529d008ae2da02db61473f9d\transformed\lifecycle-viewmodel-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\ced8041d0ea224d8c1d7111b258b03f8\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate-ktx:1.2.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\ced8041d0ea224d8c1d7111b258b03f8\transformed\jetified-savedstate-ktx-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\e1ce0c652e870898c214ee480cfa5893\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.savedstate:savedstate:1.2.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\e1ce0c652e870898c214ee480cfa5893\transformed\jetified-savedstate-1.2.1\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\3df553aee2a5f5aae57083cedb52c47e\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-viewmodel-savedstate:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\3df553aee2a5f5aae57083cedb52c47e\transformed\jetified-lifecycle-viewmodel-savedstate-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core-ktx:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\ae4c261d653bd49f5b92bfef76ab4d2d\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.core:core-ktx:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\ae4c261d653bd49f5b92bfef76ab4d2d\transformed\jetified-core-ktx-1.9.0\AndroidManifest.xml:5:5-44
MERGED from [androidx.autofill:autofill:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\932f57987d994be3862f97e80f511128\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.autofill:autofill:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\932f57987d994be3862f97e80f511128\transformed\jetified-autofill-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4d55a293d403b9cf73bd36431b123f6\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.appcompat:appcompat-resources:1.1.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4d55a293d403b9cf73bd36431b123f6\transformed\jetified-appcompat-resources-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\5bf77793adb19aca6e143d091bed880f\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.drawerlayout:drawerlayout:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\5bf77793adb19aca6e143d091bed880f\transformed\drawerlayout-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\235a8531292a6dd263ba912db4ff835b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.viewpager:viewpager:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\235a8531292a6dd263ba912db4ff835b\transformed\viewpager-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\de9607de16116170e4ec920a3d531453\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable-animated:1.1.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\de9607de16116170e4ec920a3d531453\transformed\vectordrawable-animated-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\c2de4622f43fdcb84d47977c1d86ddcd\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.vectordrawable:vectordrawable:1.1.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\c2de4622f43fdcb84d47977c1d86ddcd\transformed\vectordrawable-1.1.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.customview:customview:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\8b93828b6f0a3e18bdbc32b949aad7b0\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.customview:customview:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\8b93828b6f0a3e18bdbc32b949aad7b0\transformed\customview-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\d68cc036a2de5527e7a8088fc46ea908\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\d68cc036a2de5527e7a8088fc46ea908\transformed\lifecycle-runtime-2.6.2\AndroidManifest.xml:5:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\263c5fa5ec182735fe9726c6a053e6e4\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.lifecycle:lifecycle-runtime-ktx:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\263c5fa5ec182735fe9726c6a053e6e4\transformed\jetified-lifecycle-runtime-ktx-2.6.2\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\220ed81d14c5e53d85ff494b89f0810f\transformed\jetified-runtime-saveable-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime-saveable:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\220ed81d14c5e53d85ff494b89f0810f\transformed\jetified-runtime-saveable-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\2fdbcf7e54ac151e4391bd2d88f3b41d\transformed\jetified-runtime-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.compose.runtime:runtime:1.4.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\2fdbcf7e54ac151e4391bd2d88f3b41d\transformed\jetified-runtime-1.4.0\AndroidManifest.xml:20:5-44
MERGED from [com.github.zhaolewei:Logger:1.0.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\bfca0ae0b2fab14f7213612309dac8f6\transformed\jetified-Logger-1.0.2\AndroidManifest.xml:7:5-9:41
MERGED from [com.github.zhaolewei:Logger:1.0.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\bfca0ae0b2fab14f7213612309dac8f6\transformed\jetified-Logger-1.0.2\AndroidManifest.xml:7:5-9:41
MERGED from [:recorderlib] D:\Terminus\ZlwAudioRecorder\recorderlib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [:recorderlib] D:\Terminus\ZlwAudioRecorder\recorderlib\build\intermediates\merged_manifest\debug\AndroidManifest.xml:5:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:21:5-44
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\438d9746f04723b5e421bd604bd47109\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.versionedparcelable:versionedparcelable:1.1.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\438d9746f04723b5e421bd604bd47109\transformed\versionedparcelable-1.1.1\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\bc975d9d0891b4d3eb064dcdf7727a99\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.arch.core:core-runtime:2.2.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\bc975d9d0891b4d3eb064dcdf7727a99\transformed\core-runtime-2.2.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\40aa313f9bb2163d4fe7469629902cd8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.cursoradapter:cursoradapter:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\40aa313f9bb2163d4fe7469629902cd8\transformed\cursoradapter-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\7f55c2808d12cbf97bc3331071c16400\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\7f55c2808d12cbf97bc3331071c16400\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:21:5-23:41
MERGED from [androidx.tracing:tracing:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\fd3d8bb60b5e099f49c2f660c186f9bc\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.tracing:tracing:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\fd3d8bb60b5e099f49c2f660c186f9bc\transformed\jetified-tracing-1.0.0\AndroidManifest.xml:20:5-22:41
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\d4b50ca9aea5ab4fc9c1aa3252702ae2\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.interpolator:interpolator:1.0.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\d4b50ca9aea5ab4fc9c1aa3252702ae2\transformed\interpolator-1.0.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\35c434c7e161f07e04d97a3e66c54cad\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
MERGED from [androidx.annotation:annotation-experimental:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\35c434c7e161f07e04d97a3e66c54cad\transformed\jetified-annotation-experimental-1.3.0\AndroidManifest.xml:20:5-44
	android:targetSdkVersion
		INJECTED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml
	android:minSdkVersion
		INJECTED from D:\Terminus\ZlwAudioRecorder\app\src\main\AndroidManifest.xml
queries
ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:9:5-16:15
intent#action:name:android.intent.action.MAIN
ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:10:9-12:18
intent#action:name:android.intent.action.VIEW
ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:13:9-15:18
action#android.intent.action.VIEW
ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:14:13-65
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:14:21-62
activity#com.blankj.utilcode.util.UtilsTransActivity4MainProcess
ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:19:9-24:75
	android:windowSoftInputMode
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:24:13-72
	android:exported
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:22:13-37
	android:configChanges
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:21:13-74
	android:theme
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:23:13-55
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:20:13-83
activity#com.blankj.utilcode.util.UtilsTransActivity
ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:25:9-31:75
	android:multiprocess
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:29:13-40
	android:windowSoftInputMode
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:31:13-72
	android:exported
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:28:13-37
	android:configChanges
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:27:13-74
	android:theme
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:30:13-55
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:26:13-71
provider#com.blankj.utilcode.util.UtilsFileProvider
ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:33:9-41:20
	android:grantUriPermissions
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:37:13-47
	android:authorities
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:35:13-73
	android:exported
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:36:13-37
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:34:13-70
meta-data#android.support.FILE_PROVIDER_PATHS
ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:38:13-40:68
	android:resource
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:40:17-65
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:39:17-67
service#com.blankj.utilcode.util.MessengerUtils$ServerService
ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:43:9-49:19
	android:exported
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:45:13-37
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:44:13-81
intent-filter#action:name:${applicationId}.messenger
ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:46:13-48:29
intent-filter#action:name:com.zlw.audio_recorder.messenger
ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:46:13-48:29
action#${applicationId}.messenger
ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:17-69
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:25-66
action#com.zlw.audio_recorder.messenger
ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:17-69
	android:name
		ADDED from [com.blankj:utilcodex:1.31.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\92ca04b3920ec15624bc0971373881d7\transformed\jetified-utilcodex-1.31.1\AndroidManifest.xml:47:25-66
permission#${applicationId}.andpermission.bridge
ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:24:5-27:47
	android:protectionLevel
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:27:9-44
	android:permissionGroup
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:26:9-65
	android:name
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:25:9-61
permission#com.zlw.audio_recorder.andpermission.bridge
ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:24:5-27:47
	android:protectionLevel
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:27:9-44
	android:permissionGroup
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:26:9-65
	android:name
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:25:9-61
uses-permission#${applicationId}.andpermission.bridge
ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:29:5-77
	android:name
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:29:22-74
uses-permission#com.zlw.audio_recorder.andpermission.bridge
ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:29:5-77
	android:name
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:29:22-74
service#com.yanzhenjie.permission.bridge.BridgeService
ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:32:9-40:19
	android:process
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:36:13-42
	android:exported
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:34:13-37
	android:permission
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:35:13-71
	android:name
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:33:13-74
intent-filter#action:name:${applicationId}.andpermission.bridge
ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:37:13-39:29
intent-filter#action:name:com.zlw.audio_recorder.andpermission.bridge
ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:37:13-39:29
action#${applicationId}.andpermission.bridge
ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:38:17-80
	android:name
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:38:25-77
action#com.zlw.audio_recorder.andpermission.bridge
ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:38:17-80
	android:name
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:38:25-77
activity#com.yanzhenjie.permission.bridge.BridgeActivity
ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:42:9-48:76
	android:process
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:47:13-42
	android:exported
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:45:13-37
	android:permission
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:46:13-71
	android:configChanges
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:44:13-48
	android:theme
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:48:13-73
	android:name
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:43:13-75
provider#com.yanzhenjie.permission.FileProvider
ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:50:9-58:20
	android:grantUriPermissions
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:54:13-47
	android:authorities
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:52:13-67
	android:exported
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:53:13-37
	android:name
		ADDED from [com.yanzhenjie:permission:2.0.3] D:\AndroidStudio\.gradle\caches\8.12\transforms\e4060eff540ed441ee42bbd58758cdf0\transformed\jetified-permission-2.0.3\AndroidManifest.xml:51:13-66
provider#androidx.startup.InitializationProvider
ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\a8db42fd17dc95f308887add3fd2816d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\a8db42fd17dc95f308887add3fd2816d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:24:9-32:20
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\7f55c2808d12cbf97bc3331071c16400\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
MERGED from [androidx.startup:startup-runtime:1.1.1] D:\AndroidStudio\.gradle\caches\8.12\transforms\7f55c2808d12cbf97bc3331071c16400\transformed\jetified-startup-runtime-1.1.1\AndroidManifest.xml:26:9-30:34
	tools:node
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:28:13-31
	android:authorities
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:26:13-68
	android:exported
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:27:13-37
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:25:13-67
meta-data#androidx.emoji2.text.EmojiCompatInitializer
ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.emoji2:emoji2:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\58049648282e726bb76d97bc090d5d3c\transformed\jetified-emoji2-1.3.0\AndroidManifest.xml:30:17-75
meta-data#androidx.lifecycle.ProcessLifecycleInitializer
ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\a8db42fd17dc95f308887add3fd2816d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\a8db42fd17dc95f308887add3fd2816d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.lifecycle:lifecycle-process:2.6.2] D:\AndroidStudio\.gradle\caches\8.12\transforms\a8db42fd17dc95f308887add3fd2816d\transformed\jetified-lifecycle-process-2.6.2\AndroidManifest.xml:30:17-78
permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
permission#com.zlw.audio_recorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:22:5-24:47
	android:protectionLevel
		ADDED from [androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:24:9-44
	android:name
		ADDED from [androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:23:9-81
uses-permission#${applicationId}.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
uses-permission#com.zlw.audio_recorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION
ADDED from [androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:26:5-97
	android:name
		ADDED from [androidx.core:core:1.9.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\7ba74c55d38703961fa899c39b0cb95d\transformed\core-1.9.0\AndroidManifest.xml:26:22-94
meta-data#androidx.profileinstaller.ProfileInstallerInitializer
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:29:13-31:52
	android:value
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:31:17-49
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:30:17-85
receiver#androidx.profileinstaller.ProfileInstallReceiver
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:34:9-52:20
	android:enabled
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:37:13-35
	android:exported
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:38:13-36
	android:permission
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:39:13-57
	android:directBootAware
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:36:13-44
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:35:13-76
intent-filter#action:name:androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:40:13-42:29
action#androidx.profileinstaller.action.INSTALL_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:17-91
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:41:25-88
intent-filter#action:name:androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:43:13-45:29
action#androidx.profileinstaller.action.SKIP_FILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:17-85
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:44:25-82
intent-filter#action:name:androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:46:13-48:29
action#androidx.profileinstaller.action.SAVE_PROFILE
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:17-88
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:47:25-85
intent-filter#action:name:androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:49:13-51:29
action#androidx.profileinstaller.action.BENCHMARK_OPERATION
ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:17-95
	android:name
		ADDED from [androidx.profileinstaller:profileinstaller:1.3.0] D:\AndroidStudio\.gradle\caches\8.12\transforms\57262004b660a437acd6d309a27cb0c3\transformed\jetified-profileinstaller-1.3.0\AndroidManifest.xml:50:25-92

<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:padding="16dp">

    <TextView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="选择音源"
        android:textColor="@color/text_primary"
        android:textSize="16sp"
        android:textStyle="bold"
        android:layout_marginBottom="16dp" />

    <RadioGroup
        android:id="@+id/tbSource"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <RadioButton
            android:id="@+id/rbMic"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:checked="true"
            android:text="麦克风录音"
            android:textColor="@color/text_secondary"
            android:buttonTint="@color/colorPrimary"
            android:layout_marginBottom="12dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="通过设备麦克风录制外部声音"
            android:textColor="@color/text_hint"
            android:textSize="12sp"
            android:layout_marginStart="32dp"
            android:layout_marginBottom="16dp" />

        <RadioButton
            android:id="@+id/rbSystem"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="系统内录 (Android 10+)"
            android:textColor="@color/text_secondary"
            android:buttonTint="@color/colorPrimary"
            android:layout_marginBottom="12dp" />

        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="录制系统播放的音频内容，需要Android 10及以上版本"
            android:textColor="@color/text_hint"
            android:textSize="12sp"
            android:layout_marginStart="32dp" />

    </RadioGroup>

</LinearLayout>

<resources>

    <style name="Theme.ZlwAudioRecorder" parent="Theme.AppCompat.Light.NoActionBar">
        <item name="colorPrimary">@color/colorPrimary</item>
        <item name="colorPrimaryDark">@color/colorPrimaryDark</item>
        <item name="colorAccent">@color/colorAccent</item>
        <item name="android:windowBackground">@color/background_primary</item>
        <item name="android:statusBarColor">@color/colorPrimaryDark</item>
    </style>

    <!-- 卡片样式 -->
    <style name="CardStyle">
        <item name="android:background">@drawable/card_background</item>
        <item name="android:elevation">2dp</item>
        <item name="android:layout_margin">8dp</item>
        <item name="android:padding">16dp</item>
    </style>

    <!-- 主要按钮样式 -->
    <style name="PrimaryButtonStyle">
        <item name="android:background">@drawable/button_primary</item>
        <item name="android:textColor">#FFFFFF</item>
        <item name="android:textSize">16sp</item>
        <item name="android:elevation">4dp</item>
        <item name="android:minHeight">48dp</item>
    </style>

    <!-- 次要按钮样式 -->
    <style name="SecondaryButtonStyle">
        <item name="android:background">@drawable/button_secondary</item>
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textSize">16sp</item>
        <item name="android:elevation">2dp</item>
        <item name="android:minHeight">48dp</item>
    </style>

</resources>

package com.zlw.audio_recorder.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.Button;
import android.widget.LinearLayout;
import android.widget.TextView;
import android.widget.Toast;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.zlw.audio_recorder.R;
import com.zlw.audio_recorder.model.ExhibitionRoom;
import com.zlw.audio_recorder.network.ApiClient;
import com.zlw.loggerlib.Logger;

import java.util.List;

/**
 * 选择展厅Fragment
 */
public class ExhibitionRoomFragment extends Fragment {
    private static final String TAG = ExhibitionRoomFragment.class.getSimpleName();

    private LinearLayout roomContainer;
    private TextView statusText;
    private String selectedRoom = null;
    private String sessionId = null;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.fragment_exhibition_room, container, false);
        initViews(view);
        return view;
    }

    private void initViews(View view) {
        roomContainer = view.findViewById(R.id.roomContainer);
        statusText = view.findViewById(R.id.statusText);
        
        statusText.setText("正在获取展厅列表...");
    }

    /**
     * 更新展厅列表
     */
    public void updateRoomList(List<ExhibitionRoom> rooms) {
        if (getActivity() == null) return;
        
        getActivity().runOnUiThread(() -> {
            roomContainer.removeAllViews();
            
            if (rooms == null || rooms.isEmpty()) {
                statusText.setText("暂无展厅数据");
                statusText.setVisibility(View.VISIBLE);
                return;
            }
            
            statusText.setVisibility(View.GONE);
            
            for (ExhibitionRoom room : rooms) {
                Button roomButton = createRoomButton(room);
                roomContainer.addView(roomButton);
            }
            
            Logger.i(TAG, "展厅列表已更新，共 %d 个展厅", rooms.size());
        });
    }

    /**
     * 创建展厅按钮
     */
    private Button createRoomButton(ExhibitionRoom room) {
        Button button = new Button(getContext());
        button.setText(room.getRoom());
        button.setBackgroundResource(R.drawable.button_secondary);
        button.setTextColor(getResources().getColor(R.color.text_primary));
        
        // 设置按钮样式
        LinearLayout.LayoutParams params = new LinearLayout.LayoutParams(
                ViewGroup.LayoutParams.MATCH_PARENT,
                ViewGroup.LayoutParams.WRAP_CONTENT
        );
        params.setMargins(0, 0, 0, 16);
        button.setLayoutParams(params);
        button.setPadding(24, 16, 24, 16);
        
        // 设置点击事件
        button.setOnClickListener(v -> {
            selectedRoom = room.getRoom();
            updateButtonSelection(button);

            // 调用切换展厅接口
            if (sessionId != null) {
                switchToRoom(room.getRoom());
            } else {
                Toast.makeText(getContext(), "SessionId未初始化", Toast.LENGTH_SHORT).show();
                Logger.e(TAG, "SessionId为空，无法切换展厅");
            }
        });
        
        return button;
    }

    /**
     * 更新按钮选中状态
     */
    private void updateButtonSelection(Button selectedButton) {
        // 重置所有按钮状态
        for (int i = 0; i < roomContainer.getChildCount(); i++) {
            View child = roomContainer.getChildAt(i);
            if (child instanceof Button) {
                child.setBackgroundResource(R.drawable.button_secondary);
            }
        }
        
        // 设置选中按钮状态
        selectedButton.setBackgroundResource(R.drawable.button_primary);
    }

    /**
     * 设置sessionId
     */
    public void setSessionId(String sessionId) {
        this.sessionId = sessionId;
    }

    /**
     * 获取选中的展厅
     */
    public String getSelectedRoom() {
        return selectedRoom;
    }

    /**
     * 显示错误信息
     */
    public void showError(String error) {
        if (getActivity() == null) return;

        getActivity().runOnUiThread(() -> {
            statusText.setText("获取展厅列表失败：" + error);
            statusText.setVisibility(View.VISIBLE);
            roomContainer.removeAllViews();
        });
    }

    /**
     * 切换到指定展厅
     */
    private void switchToRoom(String roomName) {
        Logger.i(TAG, "开始切换展厅: %s", roomName);

        // 显示切换中状态
        if (getActivity() != null) {
            getActivity().runOnUiThread(() -> {
                Toast.makeText(getContext(), "正在切换到：" + roomName, Toast.LENGTH_SHORT).show();
            });
        }

        // 调用切换接口
        ApiClient.switchRoom(sessionId, roomName, new ApiClient.SwitchRoomCallback() {
            @Override
            public void onSuccess(String message) {
                Logger.i(TAG, "切换展厅成功: %s", message);

                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        Toast.makeText(getContext(), "已切换到：" + roomName, Toast.LENGTH_LONG).show();

                        // 切换成功后，获取当前展厅状态以确保UI同步
                        getCurrentRoomAndUpdateSelection();
                    });
                }
            }

            @Override
            public void onError(String error) {
                Logger.e(TAG, "切换展厅失败: %s", error);

                if (getActivity() != null) {
                    getActivity().runOnUiThread(() -> {
                        Toast.makeText(getContext(), "切换展厅失败：" + error, Toast.LENGTH_LONG).show();

                        // 重置选中状态
                        selectedRoom = null;
                        resetButtonSelection();
                    });
                }
            }
        });
    }

    /**
     * 重置所有按钮选中状态
     */
    private void resetButtonSelection() {
        for (int i = 0; i < roomContainer.getChildCount(); i++) {
            View child = roomContainer.getChildAt(i);
            if (child instanceof Button) {
                child.setBackgroundResource(R.drawable.button_secondary);
            }
        }
    }

    /**
     * 根据展厅名称设置对应按钮为选中状态
     */
    public void setSelectedRoomByName(String roomName) {
        if (getActivity() == null || roomName == null || roomName.isEmpty()) {
            return;
        }

        getActivity().runOnUiThread(() -> {
            Logger.i(TAG, "设置选中展厅: %s", roomName);

            // 先重置所有按钮状态
            resetButtonSelection();

            // 查找匹配的按钮并设置为选中状态
            boolean found = false;
            for (int i = 0; i < roomContainer.getChildCount(); i++) {
                View child = roomContainer.getChildAt(i);
                if (child instanceof Button) {
                    Button button = (Button) child;
                    String buttonText = button.getText().toString();

                    if (roomName.equals(buttonText)) {
                        button.setBackgroundResource(R.drawable.button_primary);
                        selectedRoom = roomName;
                        found = true;
                        Logger.i(TAG, "找到并选中展厅按钮: %s", roomName);
                        break;
                    }
                }
            }

            if (!found) {
                Logger.w(TAG, "未找到对应的展厅按钮: %s", roomName);
            }
        });
    }

    /**
     * 获取当前展厅并更新选中状态
     */
    private void getCurrentRoomAndUpdateSelection() {
        if (sessionId == null) {
            Logger.e(TAG, "SessionId为空，无法获取当前展厅");
            return;
        }

        ApiClient.getCurrentRoom(sessionId, new ApiClient.GetCurrentRoomCallback() {
            @Override
            public void onSuccess(String currentRoom) {
                Logger.i(TAG, "获取当前展厅成功: %s", currentRoom);
                setSelectedRoomByName(currentRoom);
            }

            @Override
            public void onError(String error) {
                Logger.e(TAG, "获取当前展厅失败: %s", error);
            }
        });
    }
}

<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/background_primary"
    tools:context=".MainActivity">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="20dp">

        <!-- 标题区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/gradient_background"
            android:gravity="center"
            android:orientation="vertical"
            android:padding="24dp"
            android:layout_marginBottom="24dp"
            android:elevation="4dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="Terminus展厅拾音助手"
                android:textColor="#FFFFFF"
                android:textSize="24sp"
                android:textStyle="bold"
                android:layout_marginBottom="8dp" />

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="高品质音频采集"
                android:textColor="#FFFFFF"
                android:textSize="14sp"
                android:alpha="0.9" />

        </LinearLayout>

        <!-- 音频可视化卡片 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/card_background"
            android:orientation="vertical"
            android:padding="20dp"
            android:layout_marginBottom="20dp"
            android:elevation="2dp">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="音频可视化"
                android:textColor="@color/text_primary"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="16dp" />

            <com.zlw.audio_recorder.widget.AudioView
                android:id="@+id/audioView"
                android:layout_width="match_parent"
                android:layout_height="60dp" />

        </LinearLayout>

        <!-- 设置选项卡片 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:background="@drawable/card_background"
            android:orientation="vertical"
            android:layout_marginBottom="24dp"
            android:elevation="2dp">

            <com.google.android.material.tabs.TabLayout
                android:id="@+id/tabLayout"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:background="@color/background_card"
                app:tabTextColor="@color/text_secondary"
                app:tabSelectedTextColor="@color/colorPrimary"
                app:tabIndicatorColor="@color/colorPrimary"
                app:tabMode="fixed"
                app:tabGravity="fill" />

            <androidx.viewpager2.widget.ViewPager2
                android:id="@+id/viewPager"
                android:layout_width="match_parent"
                android:layout_height="250dp"
                android:layout_marginTop="16dp"
                android:padding="20dp" />

        </LinearLayout>

        <!-- 录音控制区域 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:gravity="center"
            android:orientation="horizontal"
            android:layout_marginBottom="24dp">

            <Button
                android:id="@+id/btRecord"
                android:layout_width="80dp"
                android:layout_height="80dp"
                android:background="@drawable/record_button"
                android:drawableTop="@drawable/ic_microphone"
                android:text=""
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:layout_marginEnd="20dp"
                android:elevation="6dp" />

            <Button
                android:id="@+id/btStop"
                android:layout_width="60dp"
                android:layout_height="60dp"
                android:background="@drawable/stop_button"
                android:drawableTop="@drawable/ic_stop"
                android:text=""
                android:textColor="#FFFFFF"
                android:textSize="12sp"
                android:elevation="4dp" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>

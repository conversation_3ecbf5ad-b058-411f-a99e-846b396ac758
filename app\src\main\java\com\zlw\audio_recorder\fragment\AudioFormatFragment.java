package com.zlw.audio_recorder.fragment;

import android.os.Bundle;
import android.view.LayoutInflater;
import android.view.View;
import android.view.ViewGroup;
import android.widget.RadioGroup;

import androidx.annotation.NonNull;
import androidx.annotation.Nullable;
import androidx.fragment.app.Fragment;

import com.zlw.audio_recorder.MainActivity;
import com.zlw.audio_recorder.R;
import com.zlw.main.recorderlib.RecordManager;
import com.zlw.main.recorderlib.recorder.RecordConfig;

public class AudioFormatFragment extends Fragment {

    private RadioGroup rgAudioFormat;
    private RecordManager recordManager;

    @Nullable
    @Override
    public View onCreateView(@NonNull LayoutInflater inflater, @Nullable ViewGroup container, @Nullable Bundle savedInstanceState) {
        View view = inflater.inflate(R.layout.tab_audio_format, container, false);
        
        rgAudioFormat = view.findViewById(R.id.rgAudioFormat);
        recordManager = RecordManager.getInstance();
        
        initEvent();
        
        return view;
    }

    private void initEvent() {
        rgAudioFormat.setOnCheckedChangeListener(new RadioGroup.OnCheckedChangeListener() {
            @Override
            public void onCheckedChanged(RadioGroup group, int checkedId) {
                if (checkedId == R.id.rbPcm) {
                    recordManager.changeFormat(RecordConfig.RecordFormat.PCM);
                } else if (checkedId == R.id.rbMp3) {
                    recordManager.changeFormat(RecordConfig.RecordFormat.MP3);
                } else if (checkedId == R.id.rbWav) {
                    recordManager.changeFormat(RecordConfig.RecordFormat.WAV);
                }
            }
        });
    }
}

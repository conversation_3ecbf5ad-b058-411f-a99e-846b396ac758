<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
    package="com.zlw.audio_recorder"
    android:versionCode="1"
    android:versionName="1.0" >

    <uses-sdk
        android:minSdkVersion="24"
        android:targetSdkVersion="24" />

    <uses-permission android:name="android.permission.INTERNET" />
    <uses-permission android:name="android.permission.RECORD_AUDIO" />
    <uses-permission android:name="android.permission.WRITE_EXTERNAL_STORAGE" />
    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />

    <queries>
        <intent>
            <action android:name="android.intent.action.MAIN" />
        </intent>
        <intent>
            <action android:name="android.intent.action.VIEW" />
        </intent>
    </queries>

    <permission
        android:name="com.zlw.audio_recorder.andpermission.bridge"
        android:permissionGroup="com.zlw.audio_recorder.andpermission"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.zlw.audio_recorder.andpermission.bridge" />

    <permission
        android:name="com.zlw.audio_recorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
        android:protectionLevel="signature" />

    <uses-permission android:name="com.zlw.audio_recorder.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" />

    <application
        android:name="com.zlw.audio_recorder.base.MyApp"
        android:allowBackup="true"
        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
        android:debuggable="true"
        android:extractNativeLibs="false"
        android:icon="@mipmap/ic_launcher"
        android:label="@string/app_name"
        android:roundIcon="@mipmap/ic_launcher_round"
        android:supportsRtl="true"
        android:theme="@style/Theme.ZlwAudioRecorder" >
        <activity
            android:name="com.zlw.audio_recorder.MainActivity"
            android:exported="true"
            android:theme="@style/Theme.ZlwAudioRecorder" >
            <intent-filter>
                <action android:name="android.intent.action.MAIN" />

                <category android:name="android.intent.category.LAUNCHER" />
            </intent-filter>
        </activity>
        <activity
            android:name="com.zlw.audio_recorder.TestHzActivity"
            android:label="test"
            android:screenOrientation="landscape" />

        <service
            android:name="com.zlw.main.recorderlib.recorder.RecordService"
            android:enabled="true"
            android:exported="false" />

        <activity
            android:name="com.blankj.utilcode.util.UtilsTransActivity4MainProcess"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:theme="@style/ActivityTranslucent"
            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />
        <activity
            android:name="com.blankj.utilcode.util.UtilsTransActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:exported="false"
            android:multiprocess="true"
            android:theme="@style/ActivityTranslucent"
            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />

        <provider
            android:name="com.blankj.utilcode.util.UtilsFileProvider"
            android:authorities="com.zlw.audio_recorder.utilcode.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/util_code_provider_paths" />
        </provider>

        <service
            android:name="com.blankj.utilcode.util.MessengerUtils$ServerService"
            android:exported="false" >
            <intent-filter>
                <action android:name="com.zlw.audio_recorder.messenger" />
            </intent-filter>
        </service>
        <service
            android:name="com.yanzhenjie.permission.bridge.BridgeService"
            android:exported="false"
            android:permission="com.zlw.audio_recorder.andpermission.bridge"
            android:process=":permission" >
            <intent-filter>
                <action android:name="com.zlw.audio_recorder.andpermission.bridge" />
            </intent-filter>
        </service>

        <activity
            android:name="com.yanzhenjie.permission.bridge.BridgeActivity"
            android:configChanges="orientation"
            android:exported="false"
            android:permission="com.zlw.audio_recorder.andpermission.bridge"
            android:process=":permission"
            android:theme="@style/Permission.Theme.Activity.Transparent" />

        <provider
            android:name="com.yanzhenjie.permission.FileProvider"
            android:authorities="com.zlw.audio_recorder.file.path.share"
            android:exported="false"
            android:grantUriPermissions="true" >
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/permission_file_paths" />
        </provider>
        <provider
            android:name="androidx.startup.InitializationProvider"
            android:authorities="com.zlw.audio_recorder.androidx-startup"
            android:exported="false" >
            <meta-data
                android:name="androidx.emoji2.text.EmojiCompatInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
                android:value="androidx.startup" />
            <meta-data
                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
                android:value="androidx.startup" />
        </provider>

        <receiver
            android:name="androidx.profileinstaller.ProfileInstallReceiver"
            android:directBootAware="false"
            android:enabled="true"
            android:exported="true"
            android:permission="android.permission.DUMP" >
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
            </intent-filter>
            <intent-filter>
                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
            </intent-filter>
        </receiver>
    </application>

</manifest>